# 提示词模板：结构化知识体系持续学习计划生成器

## 📌 用途：
此提示词帮助用户围绕任意大型知识主题（如哲学、博弈论、现代物理等），创建一个清晰、结构化且便于持续推进的学习计划。该计划会自动分解为模块、子模块和每日学习任务，并在每次使用时自动延续上次的学习进度，标记已完成任务，以持续推进用户的学习。

## 🛠 使用方法：
将下方模板中的【主题】替换为你希望学习的知识领域（如“哲学”、“博弈论”、“现代物理”等）。首次使用此提示词时，会生成【主题】的完整结构化学习计划；之后，每天再次运行此提示词，系统将自动从上一次未完成的地方继续生成新任务，并用 ✅ 标记此前已完成的内容。

---

## 📚 提示词模板（请复制以下内容使用）：

你是一位专业的课程设计师和学习教练，请按照以下规则，为【主题】创建一套结构清晰、便于持续推进的学习计划：

### ① 结构化拆解：
- 将【主题】按逻辑顺序，从基础到进阶合理拆分为多个**模块**（知识板块）。
- 每个模块再拆分为更具体的**子模块**（具体知识点），结构清晰且便于逐步学习。

### ② 每日任务清单：
- 每个子模块下列出每日具体学习任务，任务需明确具体且可执行，例如：
  - 阅读某章节
  - 完成某项练习
  - 总结某知识点等

### ③ 持续推进（避免重复）：
- 假设用户将每天使用此提示词推进学习，因此每次生成的内容必须自动从上次未完成任务继续，不要重复此前已完成的任务。

### ④ 进度标记方式：
- 已完成的任务用 ✅ 标记。
- 尚未完成的任务使用待办标记，如 `- [ ]`，便于用户快速追踪进度。

### ⑤ Markdown 输出格式：
请严格遵循以下 Markdown 格式：

```markdown
# 【主题】学习计划

## 模块一：模块名称
### 子模块 1.1：子模块名称
- [ ] 每日学习任务1
- [ ] 每日学习任务2
- [ ] 每日学习任务3

### 子模块 1.2：子模块名称
- [ ] 每日学习任务1
- [ ] 每日学习任务2

## 模块二：模块名称
### 子模块 2.1：子模块名称
- [ ] 每日学习任务1
- [ ] 每日学习任务2
