>化技治管工（花技治管工），管立，
>整范进成质，资沟风采干（整齐范本，成气质；资金沟通，显风采干练）
>绩，配高科，治管法（好绩配高科，治理用管法）
## 第1章信息化发展
>信设新数元
### 1.1信息与信息化
#### 1.1.1信息
#### 1.1.2信息系统
#### 1.1.3信息化
### 1.2现代化基础设施
#### 1.2.1新型基础设施建设
	新型基础设施主要包括如下三个方面：
	(1)信息基础设施。信息基础设施主要指基于新一代信息技术演化生成的基础设施。信息基础设施包括：
		①以5G、物联网、工业互联网、卫星互联网为代表的通信网络基础设施；
		②以人工智能、云计算、区块链等为代表的新技术基础设施；
		③以数据中心、智能计算中心为代表的算力基础设施等。信息基础设施凸显“技术新”。
	(2)融合基础设施。融合基础设施主要指深度应用互联网、大数据、人工智能等技术，支撑传统基础设施转型升级，进而形成的融合基础设施。融合基础设施包括智能交通基础设施、智慧能源基础设施等。融合基础设施重在“应用新”。
	(3)创新基础设施。创新基础设施主要指支撑科学研究、技术开发、产品研制的具有公益属性的基础设施。创新基础设施包括重大科技基础设施、科教基础设施、产业技术创新基础设施等。创新基础设施强调“平台新”。
#### 1.2.2工业互联网
#### 1.2.3车联网
### 1.3现代化创新发展
#### 1.3.1农业农村现代化
#### 1.3.2两化融合与智能制造
#### 1.3.3消费互联网
### 1.4数字中国
#### 1.4.1数字经济
#### 1.4.2数字政府
#### 1.4.3数字社会
#### 1.4.4数字生态
### 1.5数字化转型与元宇宙
#### 1.5.1数字化转型
#### 1.5.2元宇宙
### 1.6本章练习
## 第2章信息技术发展
>信新
### 2.1信息技术及其发展
#### 2.1.1计算机软硬件
#### 2.1.2计算机网络
#### 2.1.3存储和数据库
#### 2.1.4信息安全
#### 2.1.5信息技术的发展
### 2.2新一代信息技术及应用
#### 2.2.1物联网
#### 2.2.2云计算
#### 2.2.3大数据
#### 2.2.4区块链
#### 2.2.5人工智能
#### 2.2.6虚拟现实
### 2.3本章练习
## 第3章信息系统治理
>治审
### 3.1 IT治理
#### 3.1.1IT治理基础
#### 3.1.2IT治理体系
#### 3.1.3IT治理任务
#### 3.1.4IT治理方法与标准
### 3.2 IT审计
#### 3.2.1IT审计基础
#### 3.2.2审计方法与技术
#### 3.2.3审计流程
#### 3.2.4审计内容
### 3.3本章练习
## 第4章信息系统管理
>方要
### 4.1管理方法
#### 4.1.1管理基础
#### 4.1.2规划和组织
#### 4.1.3设计和实施
#### 4.1.4运维和服务
#### 4.1.5优化和持续改进
### 4.2管理要点
#### 4.2.1数据管理
#### 4.2.2运维管理
#### 4.2.3信息安全管理
### 4.3本章练习
## 第5章信息系统工程
>软数集安
### 5.1软件工程
#### 5.1.1架构设计
#### 5.1.2需求分析
#### 5.1.3软件设计
#### 5.1.4软件实现
#### 5.1.5部署交付
#### 5.1.6过程管理
### 5.2数据工程
#### 5.2.1数据建模
#### 5.2.2数据标准化
#### 5.2.3数据运维
#### 5.2.4数据开发利用
#### 5.2.5数据库安全
### 5.3系统集成
#### 5.3.1集成基础
#### 5.3.2网络集成
#### 5.3.3数据集成
#### 5.3.4软件集成
#### 5.3.5应用集成
### 5.4安全工程
#### 5.4.1工程概述
#### 5.4.2安全系统
#### 5.4.3工程基础
#### 5.4.4工程体系架构
### 5.5本章练习
## 第6章项目管理概论
>P基理价
### 6.1 PMBOK的发展
### 6.2项目基本要素
#### 6.2.1项目基础
#### 6.2.2项目管理的重要性
#### 6.2.3项目成功的标准
#### 6.2.4项目、项目集、项目组合和运营管理之间的关系
#### 6.2.5项目内外部运行环境
#### 6.2.6组织系统
#### 6.2.7项目管理和产品管理
### 6.3项目经理的角色
#### 6.3.1项目经理的定义
#### 6.3.2项目经理的影响力范围
#### 6.3.3项目经理的能力
### 6.4价值驱动的项目管理知识体系
#### 6.4.1项目管理原则
#### 6.4.2项目生命周期和项目阶段
#### 6.4.3项目管理过程组
#### 6.4.4项目管理知识领域
#### 6.4.5项目绩效域
#### 6.4.6价值交付系统
### 6.5本章练习
## 第7章项目立项管理
>立行估
### 7.1项目建议与立项申请
### 7.2项目可行性研究
#### 7.2.1可行性研究的内容
#### 7.2.2初步可行性研究
#### 7.2.3详细可行性研究
### 7.3项目评估与决策
### 7.4本章练习
## 第8章项目整合管理
>基过章计指，管监变结
### 8.1管理基础
#### 8.1.1执行整合
#### 8.1.2整合的复杂性
#### 8.1.3管理新实践
#### 8.1.4项目管理计划和项目文件
### 8.2项目整合管理过程
#### 8.2.1过程概述
#### 8.2.2裁剪考虑因素
#### 8.2.3敏捷与适应方法
### 8.3制定项目章程
#### 8.3.1输入
#### 8.3.2工具与技术
#### 8.3.3输出
### 8.4制订项目管理计划
#### 8.4.1输入
#### 8.4.2工具与技术
#### 8.4.3输出
### 8.5指导与管理项目工作
#### 8.5.1输入
#### 8.5.2工具与技术
#### 8.5.3输出
### 8.6管理项目知识
#### 8.6.1输入
#### 8.6.2工具与技术
#### 8.6.3输出
### 8.7监控项目工作
#### 8.7.1输入
#### 8.7.2工具与技术
#### 8.7.3输出
### 8.8实施整体变更控制
#### 8.8.1输入
#### 8.8.2工具与技术
#### 8.8.3输出
### 8.9结束项目或阶段
#### 8.9.1输入
#### 8.9.2工具与技术
#### 8.9.3输出
### 8.10本章练习
## 第9章项目范围管理
>基过规收定建确控
### 9.1管理基础
#### 9.1.1产品范围和项目范围
#### 9.1.2管理新实践
### 9.2项目范围管理过程
#### 9.2.1过程概述
#### 9.2.2裁剪考虑因素
#### 9.2.3敏捷与适应方法
### 9.3规划范围管理
#### 9.3.1输入
#### 9.3.2工具与技术
#### 9.3.3输出
### 9.4收集需求
#### 9.4.1输入
#### 9.4.2工具与技术
#### 9.4.3输出
### 9.5定义范围
#### 9.5.1输入
#### 9.5.2工具与技术
#### 9.5.3输出
### 9.6创建WBS
#### 9.6.1输入
#### 9.6.2工具与技术
#### 9.6.3输出
### 9.7确认范围
#### 9.7.1输入
#### 9.7.2工具与技术
#### 9.7.3输出
### 9.8控制范围
#### 9.8.1输入
#### 9.8.2工具与技术
#### 9.8.3输出
### 9.9本章练习
## 第10章项目进度管理
>基过规定排估定控
### 10.1管理基础
#### 10.1.1项目进度计划的定义和总要求
#### 10.1.2管理新实践
### 10.2项目进度管理过程
#### 10.2.1过程概述
#### 10.2.2裁剪考虑因素
#### 10.2.3敏捷与适应方法
### 10.3规划进度管理
#### 10.3.1输入
#### 10.3.2工具与技术
#### 10.3.3输出
### 10.4定义活动
#### 10.4.1输入
#### 10.4.2工具与技术
#### 10.4.3输出
### 10.5排列活动顺序
#### 10.5.1输入
#### 10.5.2工具与技术
#### 10.5.3输出
### 10.6估算活动持续时间
#### 10.6.1输入
#### 10.6.2工具与技术
#### 10.6.3输出
### 10.7制订进度计划
#### 10.7.1输入
#### 10.7.2工具与技术
#### 10.7.3输出
### 10.8控制进度
#### 10.8.1输入
#### 10.8.2工具与技术
#### 10.8.3输出
### 10.9本章练习
## 第11章项目成本管理
>基过规算定控
### 11.1管理基础
#### 11.1.1重要性和意义
#### 11.1.2相关术语和定义
#### 11.1.3管理新实践
### 11.2项目成本管理过程
#### 11.2.1过程概述
#### 11.2.2裁剪考虑因素
#### 11.2.3敏捷与适应方法
### 11.3规划成本管理
#### 11.3.1输入
#### 11.3.2工具与技术
#### 11.3.3输出
### 11.4估算成本
#### 11.4.1输入
#### 11.4.2工具与技术
#### 11.4.3输出
### 11.5制定预算
#### 11.5.1输入
#### 11.5.2工具与技术
#### 11.5.3输出
### 11.6控制成本
#### 11.6.1输入
#### 11.6.2工具与技术
#### 11.6.3输出
### 11.7本章练习
## 第12章项目质量管理
>基过规管控
### 12.1管理基础
#### 12.1.1质量与项目质量
#### 12.1.2质量管理
#### 12.1.3质量管理标准体系
#### 12.1.4管理新实践
### 12.2项目质量管理过程
#### 12.2.1过程概述
#### 12.2.2裁剪考虑因素
#### 12.2.3敏捷与适应方法
### 12.3规划质量管理
#### 12.3.1输入
#### 12.3.2工具与技术
#### 12.3.3输出
### 12.4管理质量
#### 12.4.1输入
#### 12.4.2工具与技术
#### 12.4.3输出
### 12.5控制质量
#### 12.5.1输入
#### 12.5.2工具与技术
#### 12.5.3输出
### 12.6本章练习
## 第13章项目资源管理
>基过规算取建管控
### 13.1管理基础
#### 13.1.1相关术语和定义
#### 13.1.2管理新实践
### 13.2项目资源管理过程
#### 13.2.1过程概述
#### 13.2.2裁剪考虑因素
#### 13.2.3敏捷与适应方法
### 13.3规划资源管理
#### 13.3.1输入
#### 13.3.2工具与技术
#### 13.3.3输出
### 13.4估算活动资源
#### 13.4.1输入
#### 13.4.2工具与技术
#### 13.4.3输出
### 13.5获取资源
#### 13.5.1输入
#### 13.5.2工具与技术
#### 13.5.3输出
### 13.6建设团队
#### 13.6.1输入
#### 13.6.2工具与技术
#### 13.6.3输出
### 13.7管理团队
#### 13.7.1输入
#### 13.7.2工具与技术
#### 13.7.3输出
### 13.8控制资源
#### 13.8.1输入
#### 13.8.2工具与技术
#### 13.8.3输出
### 13.9本章练习
## 第14章项目沟通管理
>基过规管监
### 14.1管理基础
#### 14.1.1沟通
#### 14.1.2沟通模型
#### 14.1.3沟通分类
#### 14.1.4沟通技巧
#### 14.1.5管理新实践
### 14.2项目沟通管理过程
#### 14.2.1过程概述
#### 14.2.2裁剪考虑因素
#### 14.2.3敏捷与适应方法
### 14.3规划沟通管理
#### 14.3.1输入
#### 14.3.2工具与技术
#### 14.3.3输出
### 14.4管理沟通
#### 14.4.1输入
#### 14.4.2工具与技术
#### 14.4.3输出
### 14.5监督沟通
#### 14.5.1输入
#### 14.5.2工具与技术
#### 14.5.3输出
### 14.6本章练习
## 第15章项目风险管理
>基过规识性，量规施监例
### 15.1管理基础
#### 15.1.1项目风险概述
#### 15.1.2风险的属性
#### 15.1.3风险的分类
#### 15.1.4风险成本及其负担
#### 15.1.5管理新实践
### 15.2项目风险管理过程
#### 15.2.1过程概述
#### 15.2.2裁剪考虑因素
#### 15.2.3敏捷与适应方法
### 15.3规划风险管理
#### 15.3.1输入
#### 15.3.2工具与技术
#### 15.3.3输出
### 15.4识别风险
#### 15.4.1输入
#### 15.4.2工具与技术
#### 15.4.3输出
### 15.5实施定性风险分析
#### 15.5.1输入
#### 15.5.2工具与技术
#### 15.5.3输出
### 15.6实施定量风险分析
#### 15.6.1输入
#### 15.6.2工具与技术
#### 15.6.3输出
### 15.7规划风险应对
#### 15.7.1输入
#### 15.7.2工具与技术
#### 15.7.3输出
### 15.8实施风险应对
#### 15.8.1输入
#### 15.8.2工具与技术
#### 15.8.3输出
### 15.9监督风险
#### 15.9.1输入
#### 15.9.2工具与技术
#### 15.9.3输出
### 15.10风险管理示例
### 15.11本章练习
## 第16章项目采购管理
>基过规施控合
### 16.1管理基础
#### 16.1.1协议/采购合同
#### 16.1.2管理新实践
### 16.2项目采购管理过程
#### 16.2.1过程概述
#### 16.2.2裁剪考虑因素
#### 16.2.3敏捷与适应方法
### 16.3规划采购管理
#### 16.3.1输入
#### 16.3.2工具与技术
#### 16.3.3输出
### 16.4实施采购
#### 16.4.1输入
#### 16.4.2工具与技术
#### 16.4.3输出
### 16.5控制采购
#### 16.5.1输入
#### 16.5.2工具与技术
#### 16.5.3输出
### 16.6项目合同管理
#### 16.6.1合同的类型
#### 16.6.2合同的内容
#### 16.6.3合同管理过程
### 16.7本章练习
## 第17章项目干系人管理
>基过识规管监
### 17.1管理基础
#### 17.1.1管理的重要性
#### 17.1.2管理新实践
### 17.2项目干系人管理过程
#### 17.2.1过程概述
#### 17.2.2裁剪考虑因素
#### 17.2.3敏捷与适应方法
### 17.3识别干系人
#### 17.3.1输入
#### 17.3.2工具与技术
#### 17.3.3输出
### 17.4规划干系人参与
#### 17.4.1输入
#### 17.4.2工具与技术
#### 17.4.3输出
### 17.5管理干系人参与
#### 17.5.1输入
#### 17.5.2工具与技术
#### 17.5.3输出
### 17.6监督干系人参与
#### 17.6.1输入
#### 17.6.2工具与技术
#### 17.6.3输出
### 17.7本章练习
## 第18章项目绩效域
### 18.1干系人绩效域
>干团生规工，交度不
#### 18.1.1绩效要点
#### 18.1.2与其他绩效域的相互作用
#### 18.1.3执行效果检查
### 18.2团队绩效域
#### 18.2.1绩效要点
#### 18.2.2与其他绩效域的相互作用
#### 18.2.3执行效果检查
### 18.3开发方法和生命周期绩效域
#### 18.3.1绩效要点
#### 18.3.2与其他绩效域的相互作用
#### 18.3.3执行效果检查
### 18.4规划绩效域
#### 18.4.1绩效要点
#### 18.4.2与其他绩效域的相互作用
#### 18.4.3执行效果检查
### 18.5项目工作绩效域
#### 18.5.1绩效要点
#### 18.5.2与其他绩效域的相互作用
#### 18.5.3执行效果检查
### 18.6交付绩效域
#### 18.6.1绩效要点
#### 18.6.2与其他绩效域的相互作用
#### 18.6.3执行效果检查
### 18.7度量绩效域
#### 18.7.1绩效要点
#### 18.7.2与其他绩效域的相互作用
#### 18.7.3执行效果检查
### 18.8不确定性绩效域
#### 18.8.1绩效要点
#### 18.8.2与其他绩效域的相互作用
#### 18.8.3执行效果检查
### 18.9本章练习
## 第19章配置与变更管理
>配变文
### 19.1配置管理
#### 19.1.1管理基础
#### 19.1.2角色与职责
#### 19.1.3目标与方针
#### 19.1.4管理活动
### 19.2变更管理
#### 19.2.1管理基础
#### 19.2.2管理原则
#### 19.2.3角色与职责
#### 19.2.4工作程序
#### 19.2.5变更控制
#### 19.2.6版本发布和回退计划
### 19.3项目文档管理
#### 19.3.1管理基础
#### 19.3.2规则和方法
### 19.4本章练习
## 第20章高级项目管理
>集组织量践
### 20.1项目集管理
#### 20.1.1项目集管理标准
#### 20.1.2项目集管理角色和职责
#### 20.1.3项目集管理绩效域
### 20.2项目组合管理
#### 20.2.1项目组合管理标准
#### 20.2.2项目组合管理角色和职责
#### 20.2.3项目组合管理绩效域
### 20.3组织级项目管理
#### 20.3.1组织级项目管理标准
#### 20.3.2业务价值与业务评估
#### 20.3.3 OPM框架要素
#### 20.3.4 OPM成熟度模型
### 20.4量化项目管理
#### 20.4.1量化管理理论及应用
#### 20.4.2组织级量化管理
#### 20.4.3项目级量化管理
### 20.5项目管理实践模型
#### 20.5.1 CMMI模型
#### 20.5.2 PRINCE2模型
### 20.6本章练习
## 第21章项目管理科学基础
>工运
### 21.1工程经济学
#### 21.1.1资金的时间价值与等值计算
#### 21.1.2项目经济评价
### 21.2运筹学
#### 21.2.1线性规划
#### 21.2.2运输问题
#### 21.2.3指派问题
#### 21.2.4动态规划
#### 21.2.5图与网络
#### 21.2.6博弈论
#### 21.2.7决策分析
### 21.3本章练习
## 第22章组织通用治理
>战绩型
### 22.1组织战略
#### 22.1.1组织战略要点
#### 22.1.2组织定位
#### 22.1.3组织环境分析
#### 22.1.4组织能力确认
#### 22.1.5创新和改进
### 22.2绩效考核
#### 22.2.1绩效计划
#### 22.2.2绩效实施
#### 22.2.3绩效治理
#### 22.2.4绩效评估
#### 22.2.5绩效评价结果反馈
#### 22.2.6绩效评价结果应用
### 22.3转型升级
#### 22.3.1战略转型升级
#### 22.3.2数字化转型实施
### 22.4本章练习
## 第23章组织通用管理
>人流知市
### 23.1人力资源管理
#### 23.1.1人力资源管理基础
#### 23.1.2工作分析与岗位设计
#### 23.1.3人力资源战略与计划
#### 23.1.4人员招聘与录用
#### 23.1.5人员培训
#### 23.1.6组织薪酬管理
#### 23.1.7人员职业规划与管理
### 23.2流程管理
#### 23.2.1流程基础
#### 23.2.2流程规划
#### 23.2.3流程执行
#### 23.2.4流程评价
#### 23.2.5流程持续改进
### 23.3知识管理
#### 23.3.1知识管理基础
#### 23.3.2知识价值链
#### 23.3.3显性知识与隐性知识
#### 23.3.4知识管理过程
#### 23.3.5知识协同与创新
#### 23.3.6知识传播与服务
### 23.4市场营销
#### 23.4.1营销基础
#### 23.4.2营销环境
#### 23.4.3营销分析
#### 23.4.4营销管控
### 23.5本章练习
## 第24章法律法规与标准规范
>法标
### 24.1法律法规
#### 24.1.1民法典(合同编)
#### 24.1.2招标投标法
#### 24.1.3政府采购法
#### 24.1.4专利法
#### 24.1.5著作权法
#### 24.1.6商标法
#### 24.1.7网络安全法
#### 24.1.8数据安全法
### 24.2标准规范
#### 24.2.1系统与软件工程标准
#### 24.2.2新一代信息技术标准
#### 24.2.3信息技术服务标准
### 24.3本章练习
