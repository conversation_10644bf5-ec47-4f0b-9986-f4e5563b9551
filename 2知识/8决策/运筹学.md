# 第1章　导论
## 1.1　运筹学的含义和发展
### 1.1.1　运筹学的含义
### 1.1.2　运筹学的发展
## 1.2　运筹学的特点和分析步骤
### 1.2.1　运筹学的特点
### 1.2.2　运筹学的分析步骤
## 1.3　运筹学的研究内容
## 1.4　运筹学的应用
# 第2章　线性规划
## 2.1　线性规划基础
### 2.1.1　线性规划问题及其数学模型
### 2.1.2　线性规划问题的标准型
## 2.2　图解线性规划
### 2.2.1　线性规划问题的图解法
### 2.2.2　线性规划问题的基本概念
## 2.3　单纯形法
### 2.3.1　单纯形法的基本思想
### 2.3.2　单纯形表
## 2.4　单纯形法的进一步讨论（大M法和二阶段法）
### 2.4.1　大M法
### 2.4.2　二阶段法
## 2.5　改进的单纯形法
### 2.5.1　矩阵形式的单纯形法
### 2.5.2　改进单纯形法的步骤
## 2.6　应用举例
## 2.7　 Excel的应用
# 第3章　线性规划对偶理论及其应用
## 3.1　线性规划对偶问题的提出
### 3.1.1　对偶问题的提出
### 3.1.2　对偶问题的形式
## 3.2　对偶问题的基本性质
### 3.2.1　对称性定理
### 3.2.2　弱对偶性定理
### 3.2.3　最优性定理
### 3.2.4　强对偶性定理（或称对偶定理）
### 3.2.5　互补松弛定理
## 3.3　影子价格
### 3.3.1　影子价格的概念
### 3.3.2　影子价格的经济含义
## 3.4　对偶单纯形法
### 3.4.1　对偶单纯形法的基本思想
### 3.4.2　对偶单纯形法的主要步骤
## 3.5　灵敏度分析
### 3.5.1　目标函数系数cj变化
### 3.5.2　约束条件右端向量b的变化
### 3.5.3　增加一种新产品
### 3.5.4　增加一个新的约束条件
### 3.5.5　约束条件系数aij的变化
# 第4章　运输问题
## 4.1　运输问题的典型数学模型
### 4.1.1　问题的提出
### 4.1.2　运输问题的典型数学模型
## 4.2　表上作业法
### 4.2.1　确定初始基可行解
### 4.2.2　最优解的判别
### 4.2.3　解的改进———闭回路调整法
## 4.3　产销不平衡运输问题
### 4.3.1　一般产销不平衡运输问题
### 4.3.2　带弹性需求的产销不平衡运输问题
# 第5章　整数规划
## 5.1　整数规划的数学模型
### 5.1.1　整数规划问题的提出
### 5.1.2　整数规划的一般模型
## 5.2　分支定界法
## 5.3　割平面法
### 5.3.1　割平面法的基本思想
### 5.3.2　割平面法的计算步骤
## 5.4　 0-1型整数规划
### 5.4.1　0-1型整数规划的建模方法
### 5.4.2　0-1型整数规划的解法
## 5.5　指派问题
### 5.5.1　指派问题的标准形式及应用举例
### 5.5.2　指派问题的匈牙利解法
### 5.5.3　非标准形式的指派问题
# 第6章　决策论
## 6.1　决策的基本概念
### 6.1.1　决策的定义
### 6.1.2　决策的要素
### 6.1.3　决策的分类
### 6.1.4　决策的基本步骤（见图6-1）
### 6.1.5　决策中的几个问题
## 6.2　不确定型决策
### 6.2.1　乐观准则
### 6.2.2　悲观准则
### 6.2.3　折中准则
### 6.2.4　等可能性决策准则
### 6.2.5　最小后悔值准则
## 6.3　风险型决策
### 6.3.1　最大可能法
### 6.3.2　期望值准则法
## 6.4　效用决策
### 6.4.1　效用和效用值
### 6.4.2　效用曲线
### 6.4.3　效用曲线的应用
## 6.5　多目标决策
### 6.5.1　化多目标为单目标法
### 6.5.2　目标分层法
### 6.5.3　功效系数法
# 第7章　排队论
## 7.1　排队论的提出
### 7.1.1　排队论概述
### 7.1.2　排队论的发展
### 7.1.3　排队论的运用
## 7.2　排队论的基本概念
### 7.2.1　排队系统构成要素
### 7.2.2　排队系统模型分类
### 7.2.3　排队系统的数量指标
## 7.3　到达间隔分布和服务时间分布
### 7.3.1　经验分布
### 7.3.2　理论分布
## 7.4　简单的排队系统模型
### 7.4.1　到达率与服务时间不变的
### 基本排队服务系统
### 7.4.2　单服务台排队服务系统
### 7.4.3　简单的多服务台排队服务系统
## 7.5　排队系统的优化目标与最优化问题
### 7.5.1　排队系统的优化目标
### 7.5.2　排队系统的最优化问题
# 第8章　存储论
## 8.1　存储论概述
### 8.1.1　存储问题的要素
### 8.1.2　存储系统
## 8.2　 ABC管理
### 8.2.1　ABC分类法的基本思想
### 8.2.2　ABC分类实施的步骤
### 8.2.3　ABC分类管理的措施
## 8.3　库存控制技术
### 8.3.1　定量订货法
### 8.3.2　定期订货法
## 8.4　瞬时进货模型
### 8.4.1　瞬时进货、不允许缺货模型
### 8.4.2　瞬时进货、允许缺货模型
## 8.5　逐渐进货模型
### 8.5.1　逐渐进货、不允许缺货模型
### 8.5.2　逐渐进货、允许缺货模型
## 8.6　随机存储模型
### 8.6.1　（T，s，S v ）型混合策略
### 8.6.2　报童问题
# 第9章　图与网络分析
## 9.1　图与网络分析的基本问题
## 9.2　最短路径问题
### 9.2.1　最短路径问题概述
### 9.2.2　Dijkstra标号法
### 9.2.3　Floyd标号法
## 9.3　最大流问题
### 9.3.1　最大流的基本概念
### 9.3.2　网络最大流的标号法
## 9.4　最小费用最大流问题
## 9.5　中国邮递员问题
### 9.5.1　一笔画问题的基本定理
### 9.5.2　奇偶点图上作业法
### 9.5.3　旅行商问题
## 9.6　利用Excel上机解决物流路径问题
### 9.6.1　用Excel求解最短路问题
### 9.6.2　用Excel求解最大流问题
# 第10章　网络计划技术
## 10.1　网络计划概述
## 10.2　网络图
### 10.2.1　网络图中的元素
### 10.2.2　网络图中工序之间可能存在的关系
### 10.2.3　网络图的绘制原则
### 10.2.4　网络图的绘制步骤
## 10.3　网络图的关键路线以及时间参数
### 10.3.1　关键路线
### 10.3.2　时间参数
## 10.4　网络计划优化
### 10.4.1　时间优化
### 10.4.2　时间—费用优化
### 10.4.3　时间—资源优化
# 第11章　对策论
## 11.1　对策论的基本概念
### 11.1.1　对策论的基本概念
### 11.1.2　对策行为的基本要素
### 11.1.3　对策行为的分类
## 11.2　矩阵对策
### 11.2.1　矩阵对策的数学模型
### 11.2.2　矩阵对策的策略
### 11.2.3　矩阵对策的混合策略
## 11.3　非零和对策
### 11.3.1　纳什均衡（NASH EQUILIBRIUM）
### 11.3.2　无均衡对策
