# 数学基础

## 1. 线性代数

### 1.1 向量空间

- **向量的基本概念**：
  - 定义：具有大小和方向的量
  - 表示：列向量 $\mathbf{v} = [v_1, v_2, \ldots, v_n]^T$ 或行向量
  - 向量空间：满足加法封闭性和标量乘法封闭性的向量集合

- **向量运算**：
  - 加法：$\mathbf{u} + \mathbf{v} = [u_1+v_1, u_2+v_2, \ldots, u_n+v_n]^T$
  - 标量乘法：$c\mathbf{v} = [cv_1, cv_2, \ldots, cv_n]^T$
  - 点积：$\mathbf{u} \cdot \mathbf{v} = \sum_{i=1}^{n} u_i v_i = u_1v_1 + u_2v_2 + \cdots + u_nv_n$
  - 范数：$\|\mathbf{v}\| = \sqrt{\mathbf{v} \cdot \mathbf{v}} = \sqrt{\sum_{i=1}^{n} v_i^2}$

- **线性相关与线性无关**：
  - 线性组合：$c_1\mathbf{v}_1 + c_2\mathbf{v}_2 + \cdots + c_k\mathbf{v}_k$
  - 线性相关：存在不全为零的系数 $c_i$，使得 $c_1\mathbf{v}_1 + c_2\mathbf{v}_2 + \cdots + c_k\mathbf{v}_k = \mathbf{0}$
  - 线性无关：仅当所有 $c_i = 0$ 时，$c_1\mathbf{v}_1 + c_2\mathbf{v}_2 + \cdots + c_k\mathbf{v}_k = \mathbf{0}$

- **基与维数**：
  - 基：线性无关且张成整个向量空间的向量集
  - 维数：基中向量的数量
  - 标准基：$\mathbf{e}_1, \mathbf{e}_2, \ldots, \mathbf{e}_n$，其中 $\mathbf{e}_i$ 的第 $i$ 个分量为1，其余为0

### 1.2 矩阵代数

- **矩阵的基本概念**：
  - 定义：$m \times n$ 矩阵是 $m$ 行 $n$ 列的数字数组
  - 表示：$A = [a_{ij}]_{m \times n}$，其中 $a_{ij}$ 是第 $i$ 行第 $j$ 列的元素
  - 特殊矩阵：单位矩阵 $I$、零矩阵 $O$、对角矩阵、三角矩阵

- **矩阵运算**：
  - 加法：$(A + B)_{ij} = a_{ij} + b_{ij}$
  - 标量乘法：$(cA)_{ij} = c \cdot a_{ij}$
  - 矩阵乘法：$(AB)_{ij} = \sum_{k=1}^{n} a_{ik} b_{kj}$
  - 转置：$(A^T)_{ij} = a_{ji}$
  - 迹：$\text{tr}(A) = \sum_{i=1}^{n} a_{ii}$（方阵的对角线元素之和）

- **矩阵的秩与行列式**：
  - 秩：线性无关的行（或列）的最大数量
  - 行列式：$\det(A)$ 或 $|A|$，表示方阵的"体积缩放因子"
  - 性质：$\det(AB) = \det(A)\det(B)$，$\det(A^T) = \det(A)$

- **逆矩阵**：
  - 定义：若 $AB = BA = I$，则 $B$ 是 $A$ 的逆矩阵，记为 $A^{-1}$
  - 存在条件：$A$ 是可逆的（非奇异的）当且仅当 $\det(A) \neq 0$
  - 计算：$A^{-1} = \frac{1}{\det(A)}\text{adj}(A)$，其中 $\text{adj}(A)$ 是 $A$ 的伴随矩阵

### 1.3 特征值与特征向量

- **定义**：
  - 若存在非零向量 $\mathbf{v}$ 和标量 $\lambda$，使得 $A\mathbf{v} = \lambda\mathbf{v}$，则 $\lambda$ 是 $A$ 的特征值，$\mathbf{v}$ 是对应的特征向量

- **特征方程**：
  - $\det(A - \lambda I) = 0$
  - 特征多项式：$p(\lambda) = \det(A - \lambda I)$

- **对角化**：
  - 若 $n \times n$ 矩阵 $A$ 有 $n$ 个线性无关的特征向量，则 $A$ 可对角化
  - 若 $P$ 的列是 $A$ 的特征向量，则 $P^{-1}AP = D$，其中 $D$ 是以特征值为对角元素的对角矩阵

- **矩阵分解**：
  - 特征值分解：$A = PDP^{-1}$，其中 $D$ 是对角矩阵
  - 奇异值分解(SVD)：$A = U\Sigma V^T$，其中 $\Sigma$ 是对角矩阵，$U$ 和 $V$ 是正交矩阵

### 1.4 向量微积分

- **梯度**：
  - 定义：$\nabla f = [\frac{\partial f}{\partial x_1}, \frac{\partial f}{\partial x_2}, \ldots, \frac{\partial f}{\partial x_n}]^T$
  - 几何意义：函数在某点增长最快的方向

- **雅可比矩阵**：
  - 定义：$J_{ij} = \frac{\partial f_i}{\partial x_j}$
  - 用途：多元函数的线性近似

- **海森矩阵**：
  - 定义：$H_{ij} = \frac{\partial^2 f}{\partial x_i \partial x_j}$
  - 用途：描述函数的局部曲率，判断临界点的性质

## 2. 微积分

### 2.1 单变量微积分

- **极限与连续性**：
  - 极限：$\lim_{x \to a} f(x) = L$
  - 连续性：若 $\lim_{x \to a} f(x) = f(a)$，则 $f$ 在 $a$ 处连续

- **导数**：
  - 定义：$f'(x) = \lim_{h \to 0} \frac{f(x+h) - f(x)}{h}$
  - 几何意义：函数图像在某点的切线斜率
  - 常见导数规则：和差法则、乘积法则、商法则、链式法则

- **积分**：
  - 定义：$\int_a^b f(x) dx = \lim_{n \to \infty} \sum_{i=1}^{n} f(x_i^*) \Delta x$
  - 基本定理：$\int_a^b f(x) dx = F(b) - F(a)$，其中 $F'(x) = f(x)$
  - 常见积分技巧：换元法、分部积分法

### 2.2 多变量微积分

- **偏导数**：
  - 定义：$\frac{\partial f}{\partial x_i} = \lim_{h \to 0} \frac{f(x_1, \ldots, x_i+h, \ldots, x_n) - f(x_1, \ldots, x_i, \ldots, x_n)}{h}$
  - 几何意义：函数在某个变量方向上的变化率

- **全微分**：
  - 定义：$df = \sum_{i=1}^{n} \frac{\partial f}{\partial x_i} dx_i$
  - 全导数：$\frac{df}{dt} = \sum_{i=1}^{n} \frac{\partial f}{\partial x_i} \frac{dx_i}{dt}$

- **多重积分**：
  - 二重积分：$\iint_D f(x,y) dA = \iint_D f(x,y) dx dy$
  - 三重积分：$\iiint_E f(x,y,z) dV$
  - 变量替换：$\iint_D f(x,y) dx dy = \iint_{D'} f(u,v) \left| \frac{\partial(x,y)}{\partial(u,v)} \right| du dv$

- **向量场**：
  - 梯度场：$\nabla f$
  - 散度：$\nabla \cdot \mathbf{F} = \sum_{i=1}^{n} \frac{\partial F_i}{\partial x_i}$
  - 旋度：$\nabla \times \mathbf{F}$（三维向量场）

## 3. 优化理论

### 3.1 无约束优化

- **一阶必要条件**：
  - 若 $\mathbf{x}^*$ 是局部极小值点，则 $\nabla f(\mathbf{x}^*) = \mathbf{0}$
  - 临界点：梯度为零的点（可能是极小值、极大值或鞍点）

- **二阶条件**：
  - 二阶必要条件：若 $\mathbf{x}^*$ 是局部极小值点，则 $\nabla f(\mathbf{x}^*) = \mathbf{0}$ 且 $H(\mathbf{x}^*)$ 是半正定的
  - 二阶充分条件：若 $\nabla f(\mathbf{x}^*) = \mathbf{0}$ 且 $H(\mathbf{x}^*)$ 是正定的，则 $\mathbf{x}^*$ 是局部极小值点

- **梯度下降法**：
  - 迭代公式：$\mathbf{x}_{k+1} = \mathbf{x}_k - \alpha_k \nabla f(\mathbf{x}_k)$
  - 学习率：$\alpha_k > 0$
  - 收敛条件：$\|\nabla f(\mathbf{x}_k)\| < \epsilon$

- **牛顿法**：
  - 迭代公式：$\mathbf{x}_{k+1} = \mathbf{x}_k - [H(\mathbf{x}_k)]^{-1} \nabla f(\mathbf{x}_k)$
  - 收敛速度：二阶收敛（比梯度下降更快）
  - 缺点：需要计算海森矩阵的逆

### 3.2 约束优化

- **拉格朗日乘数法**：
  - 等式约束：$\min f(\mathbf{x})$ s.t. $g_i(\mathbf{x}) = 0$
  - 拉格朗日函数：$L(\mathbf{x}, \boldsymbol{\lambda}) = f(\mathbf{x}) - \sum_{i=1}^{m} \lambda_i g_i(\mathbf{x})$
  - 必要条件：$\nabla_{\mathbf{x}} L = \mathbf{0}$，$\nabla_{\boldsymbol{\lambda}} L = \mathbf{0}$

- **KKT条件**：
  - 不等式约束：$\min f(\mathbf{x})$ s.t. $g_i(\mathbf{x}) \leq 0$, $h_j(\mathbf{x}) = 0$
  - 拉格朗日函数：$L(\mathbf{x}, \boldsymbol{\lambda}, \boldsymbol{\mu}) = f(\mathbf{x}) + \sum_{i=1}^{m} \lambda_i g_i(\mathbf{x}) + \sum_{j=1}^{p} \mu_j h_j(\mathbf{x})$
  - KKT条件：
    - 稳定性：$\nabla_{\mathbf{x}} L = \mathbf{0}$
    - 原始可行性：$g_i(\mathbf{x}) \leq 0$, $h_j(\mathbf{x}) = 0$
    - 对偶可行性：$\lambda_i \geq 0$
    - 互补松弛性：$\lambda_i g_i(\mathbf{x}) = 0$

### 3.3 凸优化

- **凸集与凸函数**：
  - 凸集：对任意 $\mathbf{x}, \mathbf{y} \in C$ 和 $0 \leq t \leq 1$，有 $t\mathbf{x} + (1-t)\mathbf{y} \in C$
  - 凸函数：对任意 $\mathbf{x}, \mathbf{y} \in \text{dom}(f)$ 和 $0 \leq t \leq 1$，有 $f(t\mathbf{x} + (1-t)\mathbf{y}) \leq tf(\mathbf{x}) + (1-t)f(\mathbf{y})$
  - 严格凸函数：上述不等式严格成立（当 $\mathbf{x} \neq \mathbf{y}$ 且 $0 < t < 1$ 时）

- **凸优化问题**：
  - 标准形式：$\min f(\mathbf{x})$ s.t. $g_i(\mathbf{x}) \leq 0$, $h_j(\mathbf{x}) = 0$
  - 其中 $f$ 和 $g_i$ 是凸函数，$h_j$ 是仿射函数
  - 性质：局部最优解是全局最优解

- **对偶问题**：
  - 拉格朗日对偶函数：$g(\boldsymbol{\lambda}, \boldsymbol{\mu}) = \inf_{\mathbf{x}} L(\mathbf{x}, \boldsymbol{\lambda}, \boldsymbol{\mu})$
  - 对偶问题：$\max g(\boldsymbol{\lambda}, \boldsymbol{\mu})$ s.t. $\boldsymbol{\lambda} \geq \mathbf{0}$
  - 弱对偶性：$g(\boldsymbol{\lambda}, \boldsymbol{\mu}) \leq f(\mathbf{x}^*)$
  - 强对偶性：在某些条件下（如Slater条件），$g(\boldsymbol{\lambda}^*, \boldsymbol{\mu}^*) = f(\mathbf{x}^*)$

## 4. 数值计算方法

### 4.1 数值线性代数

- **矩阵分解**：
  - LU分解：$A = LU$，其中 $L$ 是下三角矩阵，$U$ 是上三角矩阵
  - Cholesky分解：$A = LL^T$（适用于对称正定矩阵）
  - QR分解：$A = QR$，其中 $Q$ 是正交矩阵，$R$ 是上三角矩阵

- **线性方程组求解**：
  - 直接法：高斯消元法、LU分解法
  - 迭代法：Jacobi迭代、Gauss-Seidel迭代、共轭梯度法

- **特征值计算**：
  - 幂法：求最大模特征值及其对应的特征向量
  - QR算法：计算所有特征值
  - Lanczos算法：适用于大型稀疏矩阵

### 4.2 数值优化

- **线搜索方法**：
  - 黄金分割法
  - 牛顿法
  - 割线法

- **无导数优化**：
  - Nelder-Mead单纯形法
  - 模式搜索法
  - 遗传算法、粒子群优化

- **随机优化**：
  - 随机梯度下降(SGD)
  - 动量法
  - Adam、RMSprop等自适应方法

### 4.3 数值积分

- **牛顿-科特斯公式**：
  - 梯形法则
  - 辛普森法则
  - 龙贝格积分

- **高斯求积**：
  - 高斯-勒让德求积
  - 高斯-埃尔米特求积
  - 高斯-拉盖尔求积

- **蒙特卡洛积分**：
  - 简单蒙特卡洛
  - 重要性采样
  - 马尔可夫链蒙特卡洛(MCMC)

## 5. 图论基础

### 5.1 图的基本概念

- **图的定义**：
  - 无向图：$G = (V, E)$，其中 $V$ 是顶点集，$E$ 是边集
  - 有向图：边有方向
  - 加权图：边有权重

- **图的表示**：
  - 邻接矩阵
  - 邻接表
  - 边列表

- **图的性质**：
  - 连通性
  - 路径与回路
  - 树与森林

### 5.2 图算法

- **图遍历**：
  - 深度优先搜索(DFS)
  - 广度优先搜索(BFS)
  - 拓扑排序

- **最短路径**：
  - Dijkstra算法
  - Bellman-Ford算法
  - Floyd-Warshall算法

- **最小生成树**：
  - Prim算法
  - Kruskal算法

- **网络流**：
  - 最大流问题
  - Ford-Fulkerson算法
  - 最小费用最大流

### 5.3 图与机器学习

- **图嵌入**：
  - DeepWalk
  - Node2Vec
  - Graph2Vec

- **图神经网络**：
  - 图卷积网络(GCN)
  - 图注意力网络(GAT)
  - 消息传递神经网络(MPNN)

- **图上的任务**：
  - 节点分类
  - 链接预测
  - 图分类
  - 社区检测

## 6. AI中的数学应用

### 6.1 机器学习中的数学

- **线性回归**：最小二乘法、正规方程
- **逻辑回归**：对数几率函数、最大似然估计
- **支持向量机**：拉格朗日对偶性、核技巧
- **主成分分析**：特征值分解、奇异值分解
- **聚类算法**：距离度量、K-means算法

### 6.2 深度学习中的数学

- **前向传播**：矩阵乘法、激活函数
- **反向传播**：链式法则、梯度计算
- **优化算法**：SGD、Adam、学习率调度
- **正则化**：L1/L2正则化、Dropout
- **卷积运算**：互相关、傅里叶变换

### 6.3 概率图模型

- **贝叶斯网络**：条件独立性、因果推断
- **马尔可夫随机场**：马尔可夫性、吉布斯分布
- **隐马尔可夫模型**：前向-后向算法、Viterbi算法
- **变分推断**：ELBO、变分自编码器
- **蒙特卡洛方法**：MCMC、Gibbs采样

### 6.4 强化学习中的数学

- **马尔可夫决策过程**：状态转移、奖励函数
- **值函数**：贝尔曼方程、动态规划
- **策略梯度**：REINFORCE算法、Actor-Critic方法
- **探索与利用**：多臂赌博机、UCB算法
- **函数逼近**：特征表示、深度Q网络 