# 统计学基础

## 1. 概率论基础

### 1.1 概率公理与性质

概率论是研究随机现象的数学分支，为统计学提供理论基础：

- **概率公理**：
  - 非负性：对任意事件A，P(A) ≥ 0
  - 规范性：样本空间Ω的概率P(Ω) = 1
  - 可加性：对互斥事件A和B，P(A∪B) = P(A) + P(B)

- **基本性质**：
  - 有界性：0 ≤ P(A) ≤ 1
  - 补事件概率：P(A^c) = 1 - P(A)
  - 包含关系：若A⊂B，则P(A) ≤ P(B)
  - 加法公式：P(A∪B) = P(A) + P(B) - P(A∩B)

### 1.2 条件概率与独立性

- **条件概率**：
  - 定义：P(A|B) = P(A∩B)/P(B)，当P(B) > 0
  - 乘法规则：P(A∩B) = P(B)P(A|B) = P(A)P(B|A)

- **独立性**：
  - 定义：若P(A∩B) = P(A)P(B)，则事件A和B独立
  - 条件独立：若P(A∩B|C) = P(A|C)P(B|C)，则A和B在给定C下条件独立
  - 多事件独立性：要求所有子集组合都满足独立性条件

### 1.3 贝叶斯定理

- **贝叶斯公式**：P(A|B) = P(B|A)P(A)/P(B)
- **全概率公式**：P(B) = ∑P(B|A_i)P(A_i)，其中{A_i}是互斥完备事件组
- **后验概率**：P(A_i|B) = P(B|A_i)P(A_i)/∑P(B|A_j)P(A_j)
- **应用**：医学诊断、垃圾邮件过滤、机器学习中的朴素贝叶斯分类器

## 2. 随机变量与概率分布

### 2.1 随机变量

- **定义**：从样本空间到实数集的函数
- **类型**：
  - 离散型：取值为有限个或可数无限个
  - 连续型：取值为不可数无限个（通常是区间）
- **累积分布函数(CDF)**：F(x) = P(X ≤ x)，适用于所有随机变量

### 2.2 离散型随机变量

- **概率质量函数(PMF)**：p(x) = P(X = x)
- **常见离散分布**：
  - **伯努利分布**：Bern(p)，单次成功/失败试验
    - PMF：P(X = 1) = p, P(X = 0) = 1-p
    - 均值：p，方差：p(1-p)
  
  - **二项分布**：Bin(n, p)，n次独立伯努利试验的成功次数
    - PMF：P(X = k) = C(n,k)p^k(1-p)^(n-k)
    - 均值：np，方差：np(1-p)
  
  - **泊松分布**：Pois(λ)，单位时间/空间内随机事件发生次数
    - PMF：P(X = k) = e^(-λ)λ^k/k!
    - 均值：λ，方差：λ
  
  - **几何分布**：Geo(p)，首次成功前的失败次数
    - PMF：P(X = k) = (1-p)^k·p
    - 均值：(1-p)/p，方差：(1-p)/p^2

### 2.3 连续型随机变量

- **概率密度函数(PDF)**：f(x)，满足F(x) = ∫_{-∞}^{x}f(t)dt
- **常见连续分布**：
  - **均匀分布**：Unif(a,b)，区间上等可能取值
    - PDF：f(x) = 1/(b-a)，当x∈[a,b]
    - 均值：(a+b)/2，方差：(b-a)^2/12
  
  - **正态分布**：N(μ,σ^2)，自然现象中最常见的分布
    - PDF：f(x) = (1/√(2πσ^2))e^(-(x-μ)^2/(2σ^2))
    - 均值：μ，方差：σ^2
    - 标准正态分布：N(0,1)，通过z = (x-μ)/σ标准化
  
  - **指数分布**：Exp(λ)，事件间隔时间
    - PDF：f(x) = λe^(-λx)，当x≥0
    - 均值：1/λ，方差：1/λ^2
    - 无记忆性：P(X > s+t | X > s) = P(X > t)
  
  - **伽马分布**：Gamma(α,β)，α个事件发生所需时间
    - 特例：卡方分布χ^2(k) = Gamma(k/2,1/2)

### 2.4 期望与方差

- **期望**：
  - 离散型：E[X] = ∑x·p(x)
  - 连续型：E[X] = ∫x·f(x)dx
  - 性质：E[aX+b] = aE[X]+b，E[X+Y] = E[X]+E[Y]

- **方差**：
  - 定义：Var(X) = E[(X-E[X])^2] = E[X^2] - (E[X])^2
  - 性质：Var(aX+b) = a^2·Var(X)，独立时Var(X+Y) = Var(X)+Var(Y)

- **协方差**：
  - 定义：Cov(X,Y) = E[(X-E[X])(Y-E[Y])] = E[XY] - E[X]E[Y]
  - 相关系数：ρ = Cov(X,Y)/(σ_X·σ_Y)，-1≤ρ≤1

## 3. 多维随机变量

### 3.1 联合分布与边缘分布

- **联合分布**：
  - 离散型：联合PMF p(x,y) = P(X=x, Y=y)
  - 连续型：联合PDF f(x,y)，满足F(x,y) = ∫∫_{-∞}^{x,y}f(s,t)dsdt

- **边缘分布**：
  - 离散型：p_X(x) = ∑_y p(x,y)
  - 连续型：f_X(x) = ∫f(x,y)dy

### 3.2 条件分布

- **条件分布**：
  - 离散型：p(y|x) = p(x,y)/p_X(x)
  - 连续型：f(y|x) = f(x,y)/f_X(x)

- **条件期望**：
  - E[Y|X=x]：给定X=x时Y的期望值
  - 全期望公式：E[Y] = E[E[Y|X]]

### 3.3 多元正态分布

- **二元正态分布**：
  - 参数：均值向量μ = (μ_X, μ_Y)和协方差矩阵Σ
  - 边缘分布：X ~ N(μ_X, σ_X^2)，Y ~ N(μ_Y, σ_Y^2)
  - 条件分布：X|Y=y ~ N(μ_X + ρ(σ_X/σ_Y)(y-μ_Y), σ_X^2(1-ρ^2))

- **多元正态分布**：
  - 参数：n维均值向量μ和n×n协方差矩阵Σ
  - 性质：线性组合仍为正态分布，独立性等价于不相关性

## 4. 抽样分布与中心极限定理

### 4.1 抽样分布

- **统计量**：样本的函数，如样本均值X̄、样本方差S^2
- **抽样分布**：统计量的概率分布

- **常见抽样分布**：
  - **样本均值分布**：若X_i独立同分布，均值μ，方差σ^2
    - E[X̄] = μ
    - Var(X̄) = σ^2/n
    - 若X_i服从正态分布，则X̄ ~ N(μ, σ^2/n)
  
  - **卡方分布**：若Z_i独立标准正态，则∑Z_i^2 ~ χ^2(n)
    - 自由度：n，均值：n，方差：2n
    - 应用：方差的置信区间，拟合优度检验
  
  - **t分布**：若Z ~ N(0,1)，V ~ χ^2(n)且独立，则Z/√(V/n) ~ t(n)
    - 自由度：n，均值：0（n>1时），方差：n/(n-2)（n>2时）
    - 随n增大趋近于标准正态分布
    - 应用：小样本均值推断
  
  - **F分布**：若U ~ χ^2(m)，V ~ χ^2(n)且独立，则(U/m)/(V/n) ~ F(m,n)
    - 自由度：m, n
    - 应用：方差比的检验，ANOVA

### 4.2 中心极限定理

- **定理陈述**：对于独立同分布的随机变量X_1, X_2, ..., X_n，当n足够大时，样本均值X̄的分布近似服从正态分布N(μ, σ^2/n)，无论原始分布形式如何

- **标准化形式**：(X̄ - μ)/(σ/√n) 近似服从标准正态分布N(0,1)

- **应用**：
  - 大样本均值的概率计算
  - 区间估计的理论基础
  - 抽样调查的精度评估

### 4.3 大数定律

- **弱大数定律**：当样本量n趋于无穷时，样本均值X̄依概率收敛于总体均值μ
- **强大数定律**：当样本量n趋于无穷时，样本均值X̄几乎必然收敛于总体均值μ
- **与中心极限定理区别**：大数定律描述收敛性，中心极限定理描述分布形式

## 5. 参数估计

### 5.1 点估计

- **定义**：用样本统计量估计总体参数的具体值
- **常见估计量**：
  - 样本均值：X̄ = (1/n)∑X_i，估计总体均值μ
  - 样本方差：S^2 = (1/(n-1))∑(X_i-X̄)^2，估计总体方差σ^2
  - 样本比例：p̂ = X/n，估计总体比例p

- **估计方法**：
  - **矩估计法**：基于样本矩等于总体矩的原理
  - **最大似然估计法**：选择使观测数据出现概率最大的参数值
    - 似然函数：L(θ) = f(x_1,x_2,...,x_n|θ)
    - 对数似然：l(θ) = log L(θ)，求导数=0得估计值
  - **贝叶斯估计**：结合先验信息和样本信息

- **估计量的评价标准**：
  - **无偏性**：E[θ̂] = θ
  - **有效性**：在无偏估计中方差最小
  - **一致性**：当n→∞时，θ̂→θ
  - **充分性**：包含样本中关于参数的全部信息

### 5.2 区间估计

- **定义**：以一定置信水平给出参数可能取值的区间
- **置信区间**：形如[L(X),U(X)]的随机区间，满足P(L(X) ≤ θ ≤ U(X)) = 1-α
- **置信水平**：1-α，常用值为95%、99%

- **常见参数的置信区间**：
  - **正态总体均值μ**：
    - 已知σ^2：X̄ ± z_{α/2}·σ/√n
    - 未知σ^2：X̄ ± t_{α/2}(n-1)·S/√n
  
  - **正态总体方差σ^2**：
    - [(n-1)S^2/χ^2_{α/2}(n-1), (n-1)S^2/χ^2_{1-α/2}(n-1)]
  
  - **总体比例p**：
    - p̂ ± z_{α/2}·√[p̂(1-p̂)/n]（大样本）

## 6. 假设检验

### 6.1 假设检验基本概念

- **统计假设**：关于总体参数或分布的陈述
  - 原假设H₀：通常表示"无效应"或"无差异"
  - 备择假设H₁：通常表示"有效应"或"有差异"

- **检验类型**：
  - 双侧检验：H₀: θ = θ₀ vs H₁: θ ≠ θ₀
  - 单侧检验：H₀: θ ≤ θ₀ vs H₁: θ > θ₀ 或 H₀: θ ≥ θ₀ vs H₁: θ < θ₀

- **错误类型**：
  - 第一类错误(α)：拒绝实际为真的H₀
  - 第二类错误(β)：接受实际为假的H₀
  - 检验功效：1-β，正确拒绝错误H₀的概率

- **p值**：在原假设为真的条件下，得到当前或更极端观测结果的概率
  - p值越小，证据越强烈地反对H₀
  - 当p < α时，拒绝H₀

### 6.2 常见参数检验

- **单样本均值检验**：
  - Z检验（已知σ）：Z = (X̄ - μ₀)/(σ/√n) ~ N(0,1)
  - t检验（未知σ）：t = (X̄ - μ₀)/(S/√n) ~ t(n-1)

- **两独立样本均值比较**：
  - 方差已知：Z = (X̄₁ - X̄₂ - (μ₁-μ₂)₀)/√(σ₁²/n₁ + σ₂²/n₂)
  - 方差未知但相等：t = (X̄₁ - X̄₂ - (μ₁-μ₂)₀)/S_p·√(1/n₁ + 1/n₂)，其中S_p是合并标准差
  - 方差未知且不等：Welch's t检验

- **配对样本t检验**：
  - t = d̄/(S_d/√n)，其中d̄是差值的均值，S_d是差值的标准差

- **方差检验**：
  - 单样本：χ² = (n-1)S²/σ₀² ~ χ²(n-1)
  - 两样本：F = S₁²/S₂² ~ F(n₁-1, n₂-1)

- **比例检验**：
  - 单样本：Z = (p̂ - p₀)/√[p₀(1-p₀)/n]
  - 两样本：Z = (p̂₁ - p̂₂)/√[p̂(1-p̂)(1/n₁ + 1/n₂)]，其中p̂是合并比例

### 6.3 非参数检验

- **适用场景**：总体分布未知或不满足正态性假设
- **常见方法**：
  - **符号检验**：基于正负号的分布
  - **Wilcoxon符号秩检验**：考虑差值的符号和大小
  - **Mann-Whitney U检验**：两独立样本的非参数替代
  - **Kruskal-Wallis检验**：多组比较的非参数方法
  - **Spearman等级相关系数**：非参数相关性度量

### 6.4 拟合优度检验

- **目的**：检验样本是否来自特定分布
- **卡方拟合优度检验**：
  - 统计量：χ² = ∑(O_i - E_i)²/E_i
  - 其中O_i是观测频数，E_i是期望频数
- **Kolmogorov-Smirnov检验**：基于经验分布函数与理论分布函数的最大差异

## 7. 回归分析基础

### 7.1 简单线性回归

- **模型**：Y = β₀ + β₁X + ε，其中ε ~ N(0, σ²)
- **参数估计**：
  - β̂₁ = ∑(x_i - x̄)(y_i - ȳ)/∑(x_i - x̄)²
  - β̂₀ = ȳ - β̂₁x̄
- **拟合优度**：
  - 决定系数R² = SSR/SST = 1 - SSE/SST
  - 其中SST = ∑(y_i - ȳ)²，SSR = ∑(ŷ_i - ȳ)²，SSE = ∑(y_i - ŷ_i)²
- **推断**：
  - 系数显著性检验：t = β̂₁/SE(β̂₁)
  - 预测区间和置信区间

### 7.2 多元线性回归

- **模型**：Y = β₀ + β₁X₁ + β₂X₂ + ... + βₚXₚ + ε
- **矩阵表示**：Y = Xβ + ε
- **参数估计**：β̂ = (X'X)⁻¹X'Y（最小二乘法）
- **模型评估**：
  - 整体显著性：F检验
  - 个别系数显著性：t检验
  - 调整后的R²：考虑自变量数量的拟合优度
- **多重共线性**：自变量间高度相关导致的问题
  - 诊断：方差膨胀因子(VIF)
  - 处理：变量选择、岭回归等

### 7.3 回归诊断

- **残差分析**：
  - 正态性：QQ图、Shapiro-Wilk检验
  - 同方差性：残差与拟合值散点图
  - 独立性：Durbin-Watson检验
- **影响点分析**：
  - 杠杆值：识别X空间中的极端点
  - Cook距离：衡量观测点对回归结果的影响
  - DFBETAS：衡量观测点对回归系数的影响

### 7.4 变量选择

- **目标**：选择最佳自变量子集
- **方法**：
  - 前向选择：从空模型开始，逐步添加变量
  - 后向消除：从全模型开始，逐步删除变量
  - 逐步回归：结合前向和后向方法
  - 信息准则：AIC、BIC等
- **正则化**：
  - 岭回归：添加L2惩罚项
  - Lasso回归：添加L1惩罚项，可实现变量选择

## 8. 实验设计基础

### 8.1 实验设计原理

- **基本原则**：
  - 随机化：消除系统性偏差
  - 重复：提高精度，估计误差
  - 局部控制：分组、区组等减少误差
- **术语**：
  - 因素：实验中操纵的变量
  - 水平：因素的不同取值
  - 处理：因素水平的特定组合
  - 实验单元：接受处理的基本单位

### 8.2 完全随机设计

- **特点**：随机将实验单元分配到不同处理
- **模型**：Y_ij = μ + τ_i + ε_ij
  - μ是总均值，τ_i是第i个处理效应，ε_ij是随机误差
- **分析方法**：单因素方差分析(ANOVA)
  - F = MST/MSE，其中MST是处理均方，MSE是误差均方
- **多重比较**：Tukey法、Bonferroni法等

### 8.3 随机区组设计

- **适用**：存在已知的异质性来源
- **设计**：在每个区组内随机分配处理
- **模型**：Y_ij = μ + τ_i + β_j + ε_ij
  - β_j是第j个区组效应
- **分析**：双因素方差分析，无交互作用

### 8.4 因子实验设计

- **特点**：研究多个因素及其交互作用
- **类型**：
  - 2^k设计：k个因素，每个有2个水平
  - 3^k设计：k个因素，每个有3个水平
  - 混合水平设计
- **主效应与交互效应**：
  - 主效应：单个因素的效应
  - 交互效应：因素组合的额外效应
- **分析**：多因素方差分析
