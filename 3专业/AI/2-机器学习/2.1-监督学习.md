# 监督学习

## 概述
监督学习是机器学习的一种方法，模型通过带标签的训练数据学习输入与输出之间的关系。在监督学习中，每个训练样本都包含特征（输入）和标签（期望输出），模型的目标是学习一个从输入到输出的映射函数。

### 核心原理
监督学习的核心原理可概括为：
- **学习映射函数**: f: X → Y，其中X是特征空间，Y是标签空间
- **最小化损失**: 通过最小化预测值与真实标签之间的损失函数来优化模型参数
- **泛化能力**: 模型在训练数据上表现良好的同时，也能在未见过的数据上有好的性能

### 数学表示
给定训练数据集 D = {(x₁, y₁), (x₂, y₂), ..., (xₙ, yₙ)}，监督学习的目标是找到一个函数 h(x) 使得：
- 对于分类问题：h(x) = argmax P(y|x)
- 对于回归问题：h(x) 最小化某种损失函数 L(h(x), y)，如均方误差

## 分类算法

### 逻辑回归
- **原理**: 使用Sigmoid函数将线性模型的输出映射到[0,1]区间，表示类别概率
- **数学表示**: P(y=1|x) = 1/(1+e^(-wx-b))
- **损失函数**: 交叉熵损失
- **优化方法**: 梯度下降、牛顿法
- **适用场景**: 二分类问题、需要概率输出、特征之间相关性不高
- **实现示例**:
```python
from sklearn.linear_model import LogisticRegression
model = LogisticRegression()
model.fit(X_train, y_train)
predictions = model.predict(X_test)
```

### 决策树
- **原理**: 基于特征构建树形结构，每个内部节点表示特征测试，叶节点表示类别或回归值
- **分裂标准**: 
  * 信息增益: 基于熵的减少
  * 基尼不纯度: 衡量类别混乱程度
  * 方差减少: 用于回归问题
- **优势**: 
  * 可解释性强
  * 能处理混合数据类型
  * 对缺失值较为鲁棒
- **局限性**:
  * 易过拟合
  * 对数据分布敏感
- **实现示例**:
```python
from sklearn.tree import DecisionTreeClassifier
model = DecisionTreeClassifier(criterion='gini', max_depth=5)
model.fit(X_train, y_train)
```

### 支持向量机(SVM)
- **原理**: 寻找最大间隔超平面分隔不同类别的数据点
- **数学表示**: 最小化 ||w||²/2 + C∑ξᵢ，满足 yᵢ(w·xᵢ+b) ≥ 1-ξᵢ
- **核技巧**: 通过核函数将数据映射到高维空间，解决非线性分类问题
  * 线性核: K(x,y) = x·y
  * 多项式核: K(x,y) = (γx·y + r)^d
  * RBF核: K(x,y) = exp(-γ||x-y||²)
- **超参数**: C(正则化参数)、核函数参数
- **实现示例**:
```python
from sklearn.svm import SVC
model = SVC(kernel='rbf', C=1.0, gamma='scale')
model.fit(X_train, y_train)
```

### K最近邻(KNN)
- **原理**: 基于最近的K个训练样本的多数类别进行分类
- **距离度量**:
  * 欧氏距离: sqrt(∑(xᵢ-yᵢ)²)
  * 曼哈顿距离: ∑|xᵢ-yᵢ|
  * 明可夫斯基距离: (∑|xᵢ-yᵢ|^p)^(1/p)
- **K值选择**: 较小的K值容易过拟合，较大的K值可能忽略局部特征
- **优缺点**:
  * 优点: 简单易实现、无需训练、可用于多分类
  * 缺点: 计算复杂度高、内存占用大、对特征缩放敏感
- **实现示例**:
```python
from sklearn.neighbors import KNeighborsClassifier
model = KNeighborsClassifier(n_neighbors=5, weights='uniform')
model.fit(X_train, y_train)
```

### 朴素贝叶斯
- **原理**: 基于贝叶斯定理和特征条件独立假设
- **数学表示**: P(y|x) ∝ P(y)∏P(xᵢ|y)
- **变体**:
  * 高斯朴素贝叶斯: 假设特征服从高斯分布
  * 多项式朴素贝叶斯: 适用于离散计数数据
  * 伯努利朴素贝叶斯: 适用于二值特征
- **优势**: 
  * 计算效率高
  * 需要较少的训练数据
  * 对高维数据效果好
- **实现示例**:
```python
from sklearn.naive_bayes import GaussianNB
model = GaussianNB()
model.fit(X_train, y_train)
```

### 随机森林
- **原理**: 集成多个决策树，通过投票或平均方式组合结果
- **训练过程**:
  * 随机抽样构建多个训练子集(Bootstrap采样)
  * 随机选择特征子集构建决策树
  * 组合多棵树的预测结果
- **超参数**:
  * n_estimators: 树的数量
  * max_features: 每次分裂考虑的特征数
  * max_depth: 树的最大深度
- **优势**: 降低过拟合风险、提高泛化能力、自动处理特征重要性
- **实现示例**:
```python
from sklearn.ensemble import RandomForestClassifier
model = RandomForestClassifier(n_estimators=100, max_depth=None)
model.fit(X_train, y_train)
```

### XGBoost与梯度提升树
- **原理**: 通过顺序构建树，每棵树学习前一棵树的残差
- **目标函数**: Obj = L(y, ŷ) + Ω(模型复杂度)
- **XGBoost特点**:
  * 使用二阶导数加速收敛
  * 内置正则化降低过拟合
  * 处理缺失值的策略
  * 支持并行计算
- **超参数调优**:
  * learning_rate: 学习率，控制每棵树的贡献
  * n_estimators: 树的数量
  * max_depth: 树的最大深度
  * subsample: 样本抽样比例
  * colsample_bytree: 特征抽样比例
- **实现示例**:
```python
from xgboost import XGBClassifier
model = XGBClassifier(learning_rate=0.1, n_estimators=100, max_depth=3)
model.fit(X_train, y_train)
```

## 回归算法

### 线性回归
- **原理**: 假设目标变量与特征之间存在线性关系
- **数学表示**: y = w₀ + w₁x₁ + w₂x₂ + ... + wₙxₙ + ε
- **损失函数**: 均方误差(MSE)
- **求解方法**:
  * 解析解: w = (X^T X)^(-1)X^T y
  * 梯度下降: 迭代更新权重
- **优势与局限**:
  * 优势: 简单易解释、计算效率高
  * 局限: 只能捕捉线性关系、对异常值敏感
- **实现示例**:
```python
from sklearn.linear_model import LinearRegression
model = LinearRegression()
model.fit(X_train, y_train)
```

### 多项式回归
- **原理**: 通过增加特征的高次项捕捉非线性关系
- **数学表示**: y = w₀ + w₁x + w₂x² + ... + wₙxⁿ + ε
- **模型复杂度**: 由多项式的最高次数决定
- **过拟合风险**: 高次多项式容易过拟合，需要结合正则化
- **实现方法**: 使用特征转换+线性回归
- **实现示例**:
```python
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LinearRegression
from sklearn.pipeline import Pipeline

model = Pipeline([
    ('poly', PolynomialFeatures(degree=2)),
    ('linear', LinearRegression())
])
model.fit(X_train, y_train)
```

### 岭回归与LASSO
- **岭回归(L2正则化)**:
  * 目标函数: MSE + λ∑wᵢ²
  * 收缩所有特征权重但不置零
  * 适用于多重共线性数据
  * 超参数λ控制正则化强度

- **LASSO(L1正则化)**:
  * 目标函数: MSE + λ∑|wᵢ|
  * 产生稀疏解(部分特征权重为零)
  * 实现特征选择
  * 超参数λ控制稀疏度

- **对比**:
  * 岭回归保留所有特征，适合所有特征都有贡献的场景
  * LASSO实现特征选择，适合特征冗余的场景
  * 岭回归有解析解，LASSO通常需要迭代求解

- **实现示例**:
```python
from sklearn.linear_model import Ridge, Lasso
ridge_model = Ridge(alpha=1.0)  # alpha是正则化强度
lasso_model = Lasso(alpha=1.0)
ridge_model.fit(X_train, y_train)
lasso_model.fit(X_train, y_train)
```

### 弹性网络
- **原理**: 结合L1和L2正则化
- **数学表示**: MSE + λ₁∑|wᵢ| + λ₂∑wᵢ²
- **优势**:
  * 结合了LASSO和岭回归的优点
  * 在高度相关特征存在时表现更好
  * 同时实现特征选择和处理多重共线性
- **超参数**:
  * alpha: 总正则化强度
  * l1_ratio: L1正则化的比例
- **实现示例**:
```python
from sklearn.linear_model import ElasticNet
model = ElasticNet(alpha=1.0, l1_ratio=0.5)
model.fit(X_train, y_train)
```

### 决策树回归
- **原理**: 与分类树类似，但叶节点输出连续值而非类别
- **分裂标准**: 最小化均方误差或绝对误差
- **预测**: 对新样本，根据特征值将其导航到对应的叶节点，输出该节点的平均值
- **优缺点**:
  * 优点: 可捕捉非线性关系、易于理解和可视化
  * 缺点: 容易过拟合、预测不够平滑
- **实现示例**:
```python
from sklearn.tree import DecisionTreeRegressor
model = DecisionTreeRegressor(max_depth=5)
model.fit(X_train, y_train)
```

## 评估指标

### 分类评估
- **准确率(Accuracy)**: 正确预测的样本比例
  * Accuracy = (TP + TN) / (TP + TN + FP + FN)
  * 适用于类别平衡的问题

- **精确率(Precision)**: 预测为正的样本中真正为正的比例
  * Precision = TP / (TP + FP)
  * 关注假正例的减少，适用于希望减少误报的场景

- **召回率(Recall)**: 真正为正的样本中被正确预测的比例
  * Recall = TP / (TP + FN)
  * 关注假负例的减少，适用于希望捕获所有正例的场景

- **F1分数**: 精确率和召回率的调和平均
  * F1 = 2 * (Precision * Recall) / (Precision + Recall)
  * 平衡精确率和召回率

- **ROC曲线**: 不同阈值下的真正例率(TPR)与假正例率(FPR)的曲线
  * TPR = TP / (TP + FN)
  * FPR = FP / (FP + TN)

- **AUC**: ROC曲线下的面积，衡量模型区分正负类的能力
  * AUC = 1表示完美分类
  * AUC = 0.5表示随机猜测

### 回归评估
- **均方误差(MSE)**: 预测值与真实值差的平方的平均值
  * MSE = (1/n)∑(yᵢ - ŷᵢ)²
  * 对异常值敏感

- **均方根误差(RMSE)**: MSE的平方根，与因变量同单位
  * RMSE = sqrt(MSE)
  * 常用于模型比较

- **平均绝对误差(MAE)**: 预测值与真实值差的绝对值的平均值
  * MAE = (1/n)∑|yᵢ - ŷᵢ|
  * 对异常值不如MSE敏感

- **R²(决定系数)**: 模型解释的因变量方差比例
  * R² = 1 - ∑(yᵢ - ŷᵢ)² / ∑(yᵢ - ȳ)²
  * R² = 1表示完美拟合
  * R² = 0表示模型不比预测均值更好

## 常见问题

### 过拟合与欠拟合
- **过拟合**:
  * 表现: 训练误差低但测试误差高
  * 原因: 模型过于复杂，捕捉了训练数据中的噪声
  * 解决方法:
    - 增加训练数据
    - 使用正则化
    - 减少模型复杂度
    - 早停法
    - 集成学习

- **欠拟合**:
  * 表现: 训练误差和测试误差都较高
  * 原因: 模型过于简单，无法捕捉数据的复杂模式
  * 解决方法:
    - 增加模型复杂度
    - 添加更多相关特征
    - 减少正则化强度
    - 使用更复杂的模型架构

### 偏差-方差权衡
- **偏差(Bias)**: 模型预测与真实值的系统性偏离
  * 高偏差通常导致欠拟合
  * 过于简单的模型容易有高偏差

- **方差(Variance)**: 不同训练集上模型预测的变化程度
  * 高方差通常导致过拟合
  * 过于复杂的模型容易有高方差

- **偏差-方差权衡**:
  * 降低偏差通常会增加方差，反之亦然
  * 最佳模型在两者之间取得平衡
  * 可通过学习曲线分析确定模型处于哪种状态

### 交叉验证
- **k折交叉验证**:
  * 将数据分成k份，每次使用k-1份训练，1份验证
  * 重复k次，取平均性能
  * 常用k值: 5, 10

- **留一交叉验证(LOOCV)**:
  * 特殊的k折交叉验证，k等于样本数
  * 每次只用一个样本验证
  * 计算开销大，适用于小数据集

- **分层交叉验证**:
  * 保持每个折中类别比例与原数据集一致
  * 适用于类别不平衡问题

- **实现示例**:
```python
from sklearn.model_selection import cross_val_score, KFold
kfold = KFold(n_splits=5, shuffle=True, random_state=42)
scores = cross_val_score(model, X, y, cv=kfold)
print(f"Cross-validation scores: {scores}")
print(f"Average score: {scores.mean()}")
```

### 特征工程与选择
- **特征工程**:
  * 特征创建: 从原始数据生成新特征
  * 特征变换: 对数、多项式、标准化等
  * 特征编码: 处理类别变量(独热编码、标签编码等)
  * 特征交互: 组合现有特征创建交互项

- **特征选择方法**:
  * 过滤法: 基于统计指标选择特征(方差、相关系数等)
  * 包装法: 使用模型性能评估特征子集(递归特征消除等)
  * 嵌入法: 在模型训练过程中进行选择(LASSO等)

- **维度降维**:
  * 主成分分析(PCA): 线性降维，保留最大方差方向
  * t-SNE: 非线性降维，保留局部结构
  * 自编码器: 使用神经网络学习低维表示

### 不平衡数据处理
- **数据层面方法**:
  * 上采样: 增加少数类样本(如SMOTE算法)
  * 下采样: 减少多数类样本(如随机下采样)
  * 混合采样: 结合上采样和下采样
  * 合成新样本: 生成新的少数类样本

- **算法层面方法**:
  * 代价敏感学习: 为不同类别设置不同的错误代价
  * 调整类别权重: 在训练中增加少数类的权重
  * 阈值调整: 修改分类决策阈值
  * 使用适合不平衡数据的评估指标(如F1分数、AUC)

- **集成学习方法**:
  * EasyEnsemble: 从多数类随机抽取多个子集，与少数类组合训练多个分类器
  * BalanceCascade: 迭代训练分类器，每次移除被正确分类的多数类样本

## 实践指导

### 监督学习工作流程
1. **问题定义与数据收集**
   - 明确问题类型(分类/回归)
   - 确定目标变量和评估指标
   - 收集相关数据并理解数据含义

2. **数据预处理**
   - 处理缺失值: 删除/插补
   - 处理异常值: 删除/变换/分箱
   - 特征编码: 类别变量转换
   - 特征缩放: 标准化/归一化

3. **特征工程与选择**
   - 创建新特征
   - 特征变换和交互
   - 应用特征选择方法
   - 必要时进行降维

4. **模型选择与训练**
   - 划分训练集和测试集
   - 选择合适的算法
   - 设置交叉验证策略
   - 训练基线模型

5. **模型评估与优化**
   - 使用适当指标评估模型
   - 分析模型错误
   - 超参数调优
   - 尝试集成方法

6. **模型部署与监控**
   - 模型序列化与部署
   - 设置性能监控
   - 定期重新训练和更新

### 超参数调优技术
- **网格搜索**: 对预定义参数网格进行穷举搜索
- **随机搜索**: 从参数空间随机采样，适用于高维参数空间
- **贝叶斯优化**: 基于先前结果自适应调整搜索方向
- **遗传算法**: 使用进化计算寻找最优参数组合

### 实用技巧
- **从简单模型开始**: 先建立基线模型，再逐步增加复杂度
- **特征重要性分析**: 了解哪些特征对模型贡献最大
- **学习曲线分析**: 诊断过拟合/欠拟合问题
- **模型融合**: 组合多个模型提高性能和稳定性
- **定期重新验证**: 数据分布可能随时间变化，需定期检查模型性能

## 案例研究
1. **客户流失预测**
   - 问题: 预测哪些客户可能流失
   - 算法: 逻辑回归、随机森林、XGBoost
   - 关键指标: AUC、召回率
   - 特征: 使用频率、客户满意度、产品使用时长等

2. **房价预测**
   - 问题: 预测房屋售价
   - 算法: 线性回归、岭回归、梯度提升树
   - 关键指标: RMSE、R²
   - 特征工程: 位置编码、房龄变换、面积分段等

3. **信用风险评估**
   - 问题: 预测贷款违约风险
   - 算法: 逻辑回归、随机森林、SVM
   - 处理不平衡: SMOTE上采样、代价敏感学习
   - 解释性要求: 使用可解释模型或解释黑盒模型结果 