# 非监督学习

## 概述
非监督学习是机器学习的一种方法，模型通过无标签的数据学习数据的内在结构和模式。与监督学习不同，非监督学习没有明确的输出标签，而是专注于发现数据中隐藏的规律、群组和异常。

### 核心原理
非监督学习的核心原理可概括为：
- **模式发现**：从无标签数据中识别隐藏的结构和规律
- **维度降低**：将高维数据投影到低维空间，保留关键信息
- **密度估计**：估计数据的概率分布
- **异常检测**：识别与正常模式显著偏离的数据点

### 主要任务
- **聚类**：将相似样本分组，发现数据的自然分类
- **降维**：降低数据复杂度，便于可视化和后续处理
- **异常检测**：识别异常或离群数据点
- **关联规则学习**：发现项目间的关联关系
- **生成模型**：学习数据的概率分布并生成新样本

## 聚类算法

### K均值(K-Means)
- **原理**：将数据划分为K个簇，使得每个点到其所属簇中心的距离平方和最小
- **数学表示**：最小化 $\sum_{i=1}^{k}\sum_{x \in C_i}||x-\mu_i||^2$，其中$\mu_i$是簇$C_i$的中心
- **算法步骤**：
  1. 随机初始化K个簇中心
  2. 将每个点分配给最近的簇中心
  3. 重新计算每个簇的中心（均值）
  4. 重复步骤2-3直至收敛或达到最大迭代次数
- **优势与局限**：
  * 优势：简单高效、易于理解和实现
  * 局限：需预先指定K值、对初始中心敏感、假设簇为凸形状、对异常点敏感
- **K值选择**：肘部法则、轮廓系数、间隙统计
- **实现示例**：
```python
from sklearn.cluster import KMeans
kmeans = KMeans(n_clusters=5, random_state=42)
clusters = kmeans.fit_predict(X)
```

### 层次聚类
- **原理**：通过构建簇的层次结构进行聚类，可自底向上（凝聚）或自顶向下（分裂）
- **凝聚层次聚类步骤**：
  1. 将每个点作为一个独立的簇
  2. 合并最接近的两个簇
  3. 更新簇间距离
  4. 重复2-3直到所有点合并为一个簇
- **距离度量**：
  * 单链接：两个簇中最近点对的距离
  * 全链接：两个簇中最远点对的距离
  * 平均链接：所有点对之间距离的平均值
  * Ward方法：合并后簇内方差增加最小
- **优势与局限**：
  * 优势：无需预先指定簇数、能发现层次结构、可生成树状图
  * 局限：计算复杂度高O(n³)、不适合大型数据集
- **实现示例**：
```python
from sklearn.cluster import AgglomerativeClustering
model = AgglomerativeClustering(n_clusters=5, linkage='ward')
clusters = model.fit_predict(X)
```

### DBSCAN
- **原理**：基于密度的聚类，将密度连接的点划分为一个簇，能识别任意形状的簇
- **关键概念**：
  * 核心点：半径ε内至少有MinPts个点的点
  * 边界点：不是核心点但在核心点的ε-邻域内的点
  * 噪声点：既不是核心点也不是边界点的点
- **算法步骤**：
  1. 找出所有核心点和边界点
  2. 将密度连接的核心点划分为同一簇
  3. 将每个边界点分配到其核心点所在的簇
- **参数选择**：
  * ε：领域半径，通常通过k-距离图确定
  * MinPts：最小点数，通常设为维度的2倍
- **优势与局限**：
  * 优势：无需预先指定簇数、能发现任意形状的簇、可自动识别噪声点
  * 局限：对参数敏感、难以处理不同密度的簇
- **实现示例**：
```python
from sklearn.cluster import DBSCAN
model = DBSCAN(eps=0.5, min_samples=5)
clusters = model.fit_predict(X)
```

### 高斯混合模型(GMM)
- **原理**：假设数据由多个高斯分布组成，使用期望最大化(EM)算法估计分布参数
- **数学表示**：$p(x) = \sum_{k=1}^{K}\pi_k\mathcal{N}(x|\mu_k,\Sigma_k)$，其中$\pi_k$是混合系数
- **EM算法步骤**：
  1. E步：计算每个点属于每个高斯的概率（责任）
  2. M步：更新每个高斯的参数（均值、协方差）和混合系数
  3. 重复1-2直至收敛
- **协方差类型**：
  * 球形：所有维度方差相等
  * 对角：各维度方差不同但相互独立
  * 完全：考虑维度间相关性
- **优势与局限**：
  * 优势：提供软聚类（每个点属于每个簇的概率）、能表示椭圆形簇
  * 局限：对初始化敏感、需要预设高斯数量、对异常点敏感
- **实现示例**：
```python
from sklearn.mixture import GaussianMixture
model = GaussianMixture(n_components=5, covariance_type='full')
model.fit(X)
clusters = model.predict(X)
```

### 谱聚类
- **原理**：利用数据的相似度矩阵的特征值和特征向量进行降维，然后在低维空间进行聚类
- **算法步骤**：
  1. 构建相似度矩阵
  2. 计算拉普拉斯矩阵
  3. 计算拉普拉斯矩阵的特征向量
  4. 取前k个特征向量形成新的特征空间
  5. 在此空间上应用K均值聚类
- **优势与局限**：
  * 优势：能处理复杂形状的簇、对非线性可分数据有效
  * 局限：计算复杂度高、对参数敏感、需要预先指定簇数
- **应用场景**：图像分割、社交网络分析
- **实现示例**：
```python
from sklearn.cluster import SpectralClustering
model = SpectralClustering(n_clusters=5, affinity='nearest_neighbors')
clusters = model.fit_predict(X)
```

### 自组织映射(SOM)
- **原理**：使用神经网络将高维数据映射到低维网格，保持数据的拓扑结构
- **网络结构**：包含输入层和竞争层（通常是二维网格）
- **训练过程**：
  1. 随机初始化网络权重
  2. 选择一个输入向量
  3. 找到最匹配的神经元（BMU, Best Matching Unit）
  4. 更新BMU和其邻域神经元的权重
  5. 减小学习率和邻域半径
  6. 重复2-5直至收敛
- **优势与局限**：
  * 优势：保持数据拓扑关系、可视化高维数据
  * 局限：需要预设网格大小、训练速度慢
- **实现示例**：
```python
from minisom import MiniSom
som = MiniSom(10, 10, X.shape[1], sigma=1.0, learning_rate=0.5)
som.train_random(X, 100)  # 训练100次迭代
```

## 降维算法

### 主成分分析(PCA)
- **原理**：寻找方差最大的正交投影方向，将高维数据投影到低维空间
- **数学表示**：找到特征向量满足 $Cw = \lambda w$，其中C是数据协方差矩阵
- **算法步骤**：
  1. 标准化数据
  2. 计算协方差矩阵
  3. 计算协方差矩阵的特征值和特征向量
  4. 按特征值大小排序特征向量
  5. 选择前k个特征向量组成投影矩阵
  6. 将原始数据投影到新空间
- **组分选择**：根据累积方差贡献率、碎石图
- **优势与局限**：
  * 优势：减少维度、消除多重共线性、可视化高维数据
  * 局限：仅捕捉线性关系、对异常点敏感、可能丢失重要特征
- **实现示例**：
```python
from sklearn.decomposition import PCA
pca = PCA(n_components=2)
X_reduced = pca.fit_transform(X)
```

### t-SNE
- **原理**：t分布随机邻域嵌入，专注于保持局部结构和数据点之间的相对距离
- **数学思想**：
  * 在高维空间定义点之间的条件概率，反映相似度
  * 在低维空间定义类似的概率分布
  * 最小化两个分布的KL散度
- **超参数**：
  * perplexity：估计局部邻域大小，通常5-50
  * learning_rate：学习率，影响收敛速度
  * n_iter：迭代次数
- **优势与局限**：
  * 优势：优秀的可视化能力、保持局部结构、发现集群
  * 局限：计算复杂度高O(n²)、非确定性结果、难以解释低维表示
- **实现示例**：
```python
from sklearn.manifold import TSNE
tsne = TSNE(n_components=2, perplexity=30, n_iter=1000)
X_reduced = tsne.fit_transform(X)
```

### UMAP
- **原理**：Uniform Manifold Approximation and Projection，基于黎曼几何和拓扑理论的降维方法
- **核心思想**：
  * 构建数据的拓扑表示
  * 寻找将该表示嵌入到低维空间的变换
- **超参数**：
  * n_neighbors：控制局部结构保存
  * min_dist：控制点之间的最小距离
- **优势与局限**：
  * 优势：比t-SNE更快、更好地保留全局结构、支持监督模式
  * 局限：理论复杂、超参数选择敏感
- **实现示例**：
```python
import umap
reducer = umap.UMAP(n_neighbors=15, min_dist=0.1)
X_reduced = reducer.fit_transform(X)
```

### 自编码器
- **原理**：神经网络结构，通过编码和解码过程学习数据的低维表示
- **网络结构**：
  * 编码器：将输入压缩为低维表示
  *潜在空间：数据的低维表示
  * 解码器：从低维表示重构输入
- **损失函数**：重构误差（如MSE）
- **变体**：
  * 去噪自编码器：加入噪声提高鲁棒性
  * 变分自编码器(VAE)：引入概率分布
  * 稀疏自编码器：强制激活稀疏
- **优势与局限**：
  * 优势：可处理非线性关系、可堆叠形成深度结构
  * 局限：需要大量数据、超参数调优复杂
- **实现示例**：
```python
from tensorflow.keras.layers import Input, Dense
from tensorflow.keras.models import Model

# 定义编码器
input_dim = X.shape[1]
encoding_dim = 2
input_layer = Input(shape=(input_dim,))
encoded = Dense(encoding_dim, activation='relu')(input_layer)

# 定义解码器
decoded = Dense(input_dim, activation='sigmoid')(encoded)

# 完整的自编码器
autoencoder = Model(input_layer, decoded)
autoencoder.compile(optimizer='adam', loss='mse')

# 训练
autoencoder.fit(X, X, epochs=50, batch_size=256)

# 获取编码器模型
encoder = Model(input_layer, encoded)
X_reduced = encoder.predict(X)
```

### 因子分析
- **原理**：假设观测变量是隐藏因子的线性组合加噪声
- **数学表示**：$X = \Lambda F + \epsilon$，其中$\Lambda$是因子载荷矩阵，F是因子，$\epsilon$是误差
- **算法步骤**：
  1. 计算相关或协方差矩阵
  2. 提取因子（通常使用最大似然或主成分法）
  3. 因子旋转（如varimax）提高可解释性
  4. 计算因子分数
- **因子个数选择**：特征值>1、碎石图、平行分析
- **优势与局限**：
  * 优势：考虑测量误差、提供潜变量解释、适合问卷数据分析
  * 局限：基于线性假设、可解释性挑战、对样本量要求高
- **实现示例**：
```python
from sklearn.decomposition import FactorAnalysis
fa = FactorAnalysis(n_components=5, random_state=42)
X_reduced = fa.fit_transform(X)
```

### 线性判别分析(LDA)
- **原理**：同时作为监督降维和分类方法，最大化类间方差与类内方差的比率
- **数学表示**：最大化 $J(w) = \frac{w^TS_Bw}{w^TS_Ww}$，其中$S_B$是类间散布矩阵，$S_W$是类内散布矩阵
- **算法步骤**：
  1. 计算类内和类间散布矩阵
  2. 求解广义特征值问题
  3. 选择前k个特征向量
  4. 将数据投影到新空间
- **优势与局限**：
  * 优势：考虑类别信息、适合降维后分类、可用于多类问题
  * 局限：假设数据符合高斯分布、类内协方差相等、最多提取C-1个特征（C是类别数）
- **实现示例**：
```python
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
lda = LinearDiscriminantAnalysis(n_components=2)
X_reduced = lda.fit_transform(X, y)  # 注意LDA需要标签y
```

## 异常检测

### 孤立森林
- **原理**：基于随机森林思想，通过构建随机决策树隔离异常点
- **关键思想**：
  * 正常点需要更多分割才能被隔离
  * 异常点通常更容易被隔离
- **算法步骤**：
  1. 构建多棵隔离树：每棵树随机选择特征和分割点
  2. 计算每个点的路径长度（从根到包含该点的叶节点）
  3. 异常分数 = 2^(-E(h)/c)，其中E(h)是平均路径长度，c是正规化因子
- **优势与局限**：
  * 优势：计算高效O(n log n)、无需密度估计、适用于高维数据
  * 局限：对特征缩放敏感、随机性导致结果不稳定
- **实现示例**：
```python
from sklearn.ensemble import IsolationForest
model = IsolationForest(contamination=0.1, random_state=42)
outliers = model.fit_predict(X)  # -1表示异常，1表示正常
```

### 单类SVM
- **原理**：在特征空间中找到一个最小体积的超球面，包含大部分正常数据
- **数学表示**：优化问题，最小化球半径并包含大部分数据点
- **核函数选择**：
  * 线性核：适用于线性可分数据
  * RBF核：处理非线性边界
- **超参数**：
  * nu：异常点的上限比例
  * gamma：RBF核的宽度参数
- **优势与局限**：
  * 优势：理论基础扎实、灵活的决策边界
  * 局限：计算复杂度高、参数敏感、难以处理大型数据集
- **实现示例**：
```python
from sklearn.svm import OneClassSVM
model = OneClassSVM(nu=0.1, kernel='rbf', gamma='scale')
outliers = model.fit_predict(X)  # -1表示异常，1表示正常
```

### 局部异常因子(LOF)
- **原理**：通过比较点的局部密度与其邻域点的局部密度检测异常
- **算法步骤**：
  1. 计算每个点的k-距离和可达距离
  2. 计算每个点的局部可达密度(LRD)
  3. 计算LOF值：点的邻居的平均LRD与该点LRD之比
  4. LOF值显著大于1的点为异常点
- **超参数**：
  * n_neighbors：k近邻数量
  * contamination：预期异常比例
- **优势与局限**：
  * 优势：检测基于密度的局部异常、无需假设数据分布
  * 局限：计算复杂度高O(n²)、对k值敏感
- **实现示例**：
```python
from sklearn.neighbors import LocalOutlierFactor
model = LocalOutlierFactor(n_neighbors=20, contamination=0.1)
outliers = model.fit_predict(X)  # -1表示异常，1表示正常
```

### 基于密度的方法
- **原理**：假设正常点位于高密度区域，异常点位于低密度区域
- **常见技术**：
  * 核密度估计(KDE)：估计数据概率密度函数
  * DBSCAN异常检测：识别不属于任何簇的噪声点
  * Gaussian Mixture Models(GMM)：拟合混合高斯，低概率点为异常
- **优势与局限**：
  * 优势：直观理解、能捕捉复杂结构
  * 局限：计算密集、维度灾难问题
- **实现示例（GMM异常检测）**：
```python
from sklearn.mixture import GaussianMixture
model = GaussianMixture(n_components=5, random_state=42)
model.fit(X)
densities = model.score_samples(X)
threshold = np.percentile(densities, 5)  # 底部5%作为异常
outliers = densities < threshold
```

## 关联规则学习

### Apriori算法
- **原理**：通过频繁项集挖掘关联规则，基于"频繁项集的所有子集也是频繁的"原则
- **关键概念**：
  * 支持度(Support)：项集在所有交易中出现的比例
  * 置信度(Confidence)：条件概率P(B|A)，即包含A也包含B的交易比例
  * 提升度(Lift)：规则对随机情况的改进程度，Lift(A→B) = P(B|A)/P(B)
- **算法步骤**：
  1. 找出所有频繁项集（支持度≥最小支持度）
  2. 从频繁项集生成关联规则
  3. 筛选置信度≥最小置信度的规则
- **优势与局限**：
  * 优势：理论完备、结果易解释
  * 局限：计算复杂度高、可能产生大量规则
- **实现示例**：
```python
from mlxtend.frequent_patterns import apriori, association_rules
# 假设df是二元（0/1）交易矩阵
frequent_itemsets = apriori(df, min_support=0.01, use_colnames=True)
rules = association_rules(frequent_itemsets, metric="confidence", min_threshold=0.5)
```

### FP-Growth算法
- **原理**：频繁模式增长，使用FP树数据结构避免多次数据库扫描
- **算法步骤**：
  1. 构建FP树：
     * 统计单项频率，删除非频繁项
     * 按频率降序对交易重排序
     * 构建FP树，包含项、计数和节点链接
  2. 从FP树中挖掘频繁模式
- **优势与局限**：
  * 优势：比Apriori更高效、避免候选集生成
  * 局限：内存密集型、FP树构建复杂
- **实现示例**：
```python
from mlxtend.frequent_patterns import fpgrowth, association_rules
frequent_itemsets = fpgrowth(df, min_support=0.01, use_colnames=True)
rules = association_rules(frequent_itemsets, metric="confidence", min_threshold=0.5)
```

### Eclat算法
- **原理**：Equivalence Class Transformation，使用垂直数据格式和交集运算挖掘频繁项集
- **数据表示**：{项：包含该项的交易ID集}
- **算法步骤**：
  1. 将数据转换为垂直格式
  2. 通过计算项集的TID交集生成频繁项集
  3. 递归扩展频繁项集
- **优势与局限**：
  * 优势：计算效率高、内存需求低
  * 局限：不直接产生规则、不保留项目顺序
- **实现示例**：
```python
# Python中没有内置的Eclat实现，可以使用自定义函数或第三方库
def eclat(data, min_support):
    # 数据格式转换和实现
    # ...
    return frequent_itemsets
```

## 评估与验证

### 轮廓系数
- **原理**：衡量样本与自身所在簇的相似度相对于其他簇的相似度
- **计算公式**：s(i) = (b(i) - a(i)) / max(a(i), b(i))
  * a(i)：i与同簇其他样本的平均距离（内聚度）
  * b(i)：i与最近的非同簇的平均距离（分离度）
- **解释**：
  * 接近1：样本分配合理
  * 接近0：样本在簇边界
  * 接近-1：可能分配错误
- **实现示例**：
```python
from sklearn.metrics import silhouette_score
score = silhouette_score(X, cluster_labels)
```

### Davies-Bouldin指数
- **原理**：评估簇内相似度与簇间差异的比率，较低值表示更好的聚类
- **计算**：
  1. 计算每个簇的样本分散度
  2. 计算簇间距离
  3. 计算每对簇的相似度并取最大值的平均
- **优势**：考虑了簇的紧密性和分离性，无需真实标签
- **实现示例**：
```python
from sklearn.metrics import davies_bouldin_score
score = davies_bouldin_score(X, cluster_labels)
```

### Calinski-Harabasz指数
- **原理**：也称为方差比标准，比较簇间方差与簇内方差
- **计算公式**：(簇间方差 × (n-k)) / (簇内方差 × (k-1))
  * n是样本数，k是簇数
- **解释**：更高的值表示簇更加密集且分离良好
- **实现示例**：
```python
from sklearn.metrics import calinski_harabasz_score
score = calinski_harabasz_score(X, cluster_labels)
```

### 肘部法则
- **原理**：随着簇数增加，簇内平方和(WSS)或惯性会减小，在最佳簇数处减小速率会显著变化
- **应用步骤**：
  1. 对不同的k值运行聚类算法
  2. 计算每个k值的WSS或惯性
  3. 绘制k vs WSS曲线
  4. 选择曲线"肘部"位置的k值
- **实现示例**：
```python
wcss = []
for i in range(1, 11):
    kmeans = KMeans(n_clusters=i, random_state=42)
    kmeans.fit(X)
    wcss.append(kmeans.inertia_)
plt.plot(range(1, 11), wcss)
plt.title('Elbow Method')
plt.xlabel('Number of clusters')
plt.ylabel('WCSS')
plt.show()
```

### 信息准则(AIC/BIC)
- **原理**：平衡模型复杂度和拟合优度的统计准则
- **AIC (Akaike Information Criterion)**：
  * AIC = -2log(L) + 2p
  * L是似然函数，p是参数数量
- **BIC (Bayesian Information Criterion)**：
  * BIC = -2log(L) + p·log(n)
  * 对模型复杂度惩罚更强
- **解释**：较低的AIC/BIC值表示更优的模型
- **应用**：主要用于评估概率模型（如GMM）的最优组件数
- **实现示例**：
```python
from sklearn.mixture import GaussianMixture
n_components = np.arange(1, 11)
models = [GaussianMixture(n, random_state=42).fit(X) for n in n_components]
bic = [model.bic(X) for model in models]
aic = [model.aic(X) for model in models]
plt.plot(n_components, bic, label='BIC')
plt.plot(n_components, aic, label='AIC')
plt.xlabel('Number of components')
plt.legend()
plt.show()
```

## 实践指南

### 非监督学习工作流程
1. **数据准备**
   - 数据清洗：处理缺失值和异常值
   - 特征选择：移除无关或冗余特征
   - 特征缩放：标准化或归一化数据
   - 降维：必要时减少特征数量

2. **算法选择**
   - 任务目标：聚类、降维、异常检测等
   - 数据特征：样本量、维度、稀疏性
   - 计算资源：内存限制、时间约束
   - 可解释性需求：结果是否需要易于理解

3. **参数优化**
   - 确定关键参数：如聚类算法的簇数
   - 验证策略：使用内部评估指标
   - 搜索方法：网格搜索、随机搜索
   - 稳定性分析：多次运行评估结果稳定性

4. **结果评估**
   - 定量评估：使用适当的内部评估指标
   - 定性评估：可视化结果
   - 业务解释：将技术结果转化为业务洞见
   - 敏感性分析：评估参数变化对结果的影响

5. **结果应用**
   - 集成到业务流程：用聚类结果进行客户分层
   - 特征工程：用降维结果作为新特征
   - 异常监测：部署异常检测系统
   - 结果更新：定期重新训练模型适应新数据

### 常见挑战与解决方案

1. **确定最佳聚类数量**
   - 多种方法结合：肘部法则、轮廓系数、信息准则
   - 领域知识：基于业务理解确定合理范围
   - 稳定性分析：评估不同k值结果的稳定性

2. **高维数据处理**
   - 先降维：使用PCA或t-SNE降维后再聚类
   - 特征选择：移除低方差或高相关特征
   - 适当算法：选择适合高维数据的算法(如DBSCAN)

3. **结果解释**
   - 特征重要性分析：确定哪些特征主导了聚类
   - 集群画像：计算每个簇的特征统计量
   - 可视化：降维后可视化簇结构

4. **数据缩放敏感性**
   - 标准化：z-score标准化使所有特征均值为0，标准差为1
   - 归一化：MinMax缩放使所有特征在相同范围
   - 稳健缩放：基于中位数和四分位距的缩放，对异常值不敏感

5. **处理混合数据类型**
   - 合适的距离度量：如Gower距离支持混合数据类型
   - 编码转换：将分类变量转为数值特征
   - 分开处理：对不同类型特征单独应用适当方法

### 可视化技术
1. **降维可视化**
   - PCA/t-SNE/UMAP：将高维数据投影到2D或3D空间
   - 散点图：显示降维后的数据点，颜色表示聚类
   - 交互式探索：使用工具允许旋转、缩放和过滤

2. **聚类结果可视化**
   - 热图：显示聚类结果与特征的关系
   - 雷达图：显示各簇的特征分布
   - 平行坐标图：比较多个特征维度上的簇特征

3. **层次聚类可视化**
   - 树状图(Dendrogram)：显示层次聚类的合并过程
   - 热图结合：结合热图和树状图显示数据模式

4. **异常检测可视化**
   - 箱线图：识别单变量异常
   - 散点图矩阵：多变量关系中的异常
   - 异常分数图：按异常分数排序的条形图

## 应用场景

1. **市场细分**
   - 方法：K-means、GMM
   - 目标：根据购买行为和人口统计数据识别客户群体
   - 评估：客户群体特征明显、营销策略针对性

2. **异常交易检测**
   - 方法：Isolation Forest、LOF
   - 目标：识别可能的欺诈交易
   - 评估：准确率、召回率、误报率

3. **推荐系统**
   - 方法：协同过滤、关联规则
   - 目标：发现项目间关联，推荐相似或相关项目
   - 评估：点击率、转化率

4. **图像分割**
   - 方法：K-means、谱聚类
   - 目标：将图像分割为有意义的区域
   - 评估：分割质量、计算效率

5. **文档聚类与主题发现**
   - 方法：LDA、K-means与TF-IDF
   - 目标：发现文档集合中的主题结构
   - 评估：主题连贯性、文档分组逻辑性

6. **时间序列异常检测**
   - 方法：LOF、单类SVM结合时间特征
   - 目标：检测时间序列中的异常模式
   - 评估：及时性、准确性、可解释性

7. **生物信息学**
   - 方法：层次聚类、t-SNE
   - 目标：基因表达数据分群、蛋白质结构分类
   - 评估：生物学解释、与已知分类的一致性 