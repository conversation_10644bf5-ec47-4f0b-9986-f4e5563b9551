# 自监督学习

## 1. 自监督学习概述

### 1.1 基本概念与原理

自监督学习是机器学习的一种范式，通过从数据本身自动生成监督信号，无需人工标注即可学习有用的表示：

- **定义与特点**：
  - 从数据本身构造预测任务，而非依赖外部标签
  - 利用数据的内在结构和关系作为监督信号
  - 介于监督学习和无监督学习之间的学习范式
  - 目标是学习通用且可迁移的特征表示

- **与其他学习范式的比较**：
  - 监督学习：依赖人工标注的标签，数据量受限
  - 无监督学习：无需标签，但学习目标可能不够明确
  - 半监督学习：结合少量标注数据和大量无标注数据
  - 自监督学习：利用数据内在结构创建"伪标签"，规模可扩展

- **自监督学习的优势**：
  - 利用大量未标注数据
  - 减少对人工标注的依赖
  - 学习更通用的表示
  - 提高下游任务的性能和样本效率

### 1.2 自监督学习的发展历程

- **早期探索（2000年代）**：
  - 自编码器：重构输入数据
  - Word2Vec：基于上下文预测词语
  - 图像补全：预测图像缺失部分

- **现代自监督学习（2015-2020）**：
  - NLP领域突破：Word2Vec → BERT → GPT
  - 计算机视觉领域应用：从预文本任务到对比学习
  - 多模态自监督：跨模态对比学习

- **最新趋势（2020至今）**：
  - 大规模预训练模型：基础模型
  - 多模态自监督学习：CLIP、DALL-E等
  - 自监督强化学习：环境探索与表示学习结合

### 1.3 自监督学习的一般框架

- **预训练-微调范式**：
  - 预训练阶段：在大规模无标签数据上学习通用表示
  - 微调阶段：在下游任务上使用少量标签数据进行适应

- **预训练任务设计原则**：
  - 任务难度适中：既不太简单也不太困难
  - 与下游任务相关：学到的表示应对下游任务有用
  - 避免捷径：防止模型利用简单规则而非学习有意义的表示
  - 计算效率：任务应可高效实现和优化

- **评估方法**：
  - 线性探测：冻结特征提取器，训练线性分类器
  - 微调评估：在下游任务上微调整个模型
  - 少样本学习：使用少量标签数据评估表示质量
  - 迁移学习：评估在不同领域任务上的泛化能力

## 2. 视觉自监督学习

### 2.1 基于重构的方法

- **自编码器**：
  - 基本原理：将输入编码为潜在表示，然后重构输入
  - 变体：去噪自编码器、变分自编码器(VAE)
  - 应用：图像去噪、异常检测、生成模型

- **图像修复与补全**：
  - 任务设计：预测图像中被遮挡或缺失的区域
  - Context Encoders：使用编码器-解码器架构预测缺失区域
  - 图像修复：恢复损坏或退化的图像

- **图像着色**：
  - 任务设计：从灰度图像预测彩色图像
  - 实现方法：将着色视为分类或回归问题
  - 挑战：处理多模态性（同一灰度图可能有多种合理的着色方案）

### 2.2 基于上下文的方法

- **拼图任务(Jigsaw Puzzles)**：
  - 任务设计：将图像分割成块，打乱后重新排序
  - 实现：预测正确的排列顺序
  - 学习内容：物体的空间结构和组成关系

- **旋转预测**：
  - 任务设计：预测图像的旋转角度（0°, 90°, 180°, 270°）
  - 简单有效：实现简单但能学习有用的表示
  - 局限性：可能学习到与方向相关的捷径

- **相对位置预测**：
  - 任务设计：预测图像块之间的相对位置关系
  - 实现：给定两个图像块，预测它们的相对位置
  - 学习内容：物体的空间布局和上下文关系

### 2.3 对比学习方法

- **实例判别(Instance Discrimination)**：
  - 基本思想：将同一图像的不同视图拉近，不同图像的表示推远
  - 数据增强：创建同一图像的不同视图（裁剪、旋转、颜色变换等）
  - 对比损失：最大化正样本对的相似度，最小化负样本对的相似度

- **代表性方法**：
  - **SimCLR**：
    - 设计：简单的对比框架，使用大批量和强数据增强
    - 关键组件：非线性投影头、强数据增强、大批量训练
    - 性能：在线性评估中取得当时最先进的结果

  - **MoCo系列**：
    - 设计：动量编码器和队列存储负样本
    - 优势：允许使用大量负样本而不增加批量大小
    - 发展：MoCo v2/v3进一步改进了性能

  - **BYOL**：
    - 创新：无需负样本的对比学习
    - 机制：使用目标网络和预测器防止崩溃
    - 性能：超越许多需要负样本的对比方法

  - **SwAV**：
    - 方法：在线聚类与对比学习结合
    - 创新：使用原型向量和Sinkhorn-Knopp算法
    - 优势：计算效率高，性能优异

### 2.4 掩码图像建模

- **MAE (Masked Autoencoders)**：
  - 设计：随机掩码大部分图像块，仅编码可见块
  - 解码：重构被掩码的图像块
  - 特点：高掩码比例（75%）、非对称编码器-解码器设计
  - 性能：在多种下游任务上表现优异

- **BEiT**：
  - 方法：预测被掩码图像块的视觉标记
  - 创新：使用离散VAE学习视觉词汇表
  - 架构：基于BERT的掩码预测范式

- **SimMIM**：
  - 设计：简化的掩码图像建模
  - 特点：直接预测像素值，而非离散标记
  - 优势：简单高效，易于实现

- **与对比学习的比较**：
  - 掩码建模：更关注局部细节和内容理解
  - 对比学习：更关注全局语义和不变性
  - 互补性：两种方法可以结合使用

## 3. 语言自监督学习

### 3.1 基于上下文的语言建模

- **Word2Vec**：
  - CBOW：使用上下文预测目标词
  - Skip-gram：使用目标词预测上下文
  - 负采样：提高训练效率
  - 应用：词嵌入，捕捉语义关系

- **GloVe**：
  - 原理：基于全局词共现统计
  - 目标：词向量的点积应近似词共现概率的对数
  - 特点：结合局部上下文窗口和全局统计信息

- **ELMo**：
  - 架构：双向LSTM语言模型
  - 特点：上下文相关的词表示
  - 创新：使用不同层的表示捕捉不同级别的语言信息

### 3.2 自回归语言模型

- **GPT系列**：
  - 架构：基于Transformer解码器
  - 预训练任务：预测序列中的下一个标记
  - 缩放规律：随着模型和数据规模增加，性能持续提升
  - 涌现能力：大规模模型展现出意外的能力

- **自回归预训练的优势**：
  - 预训练和微调一致性
  - 生成能力自然内置
  - 可扩展性好
  - 适用于零样本和少样本学习

- **局限性**：
  - 单向上下文限制
  - 可能偏向预测高频词
  - 在某些理解任务上不如双向模型

### 3.3 掩码语言建模

- **BERT**：
  - 架构：基于Transformer编码器
  - 预训练任务：掩码语言建模(MLM)和下一句预测(NSP)
  - MLM：随机掩码15%的标记并预测它们
  - 优势：双向上下文表示，适合理解任务

- **RoBERTa**：
  - 改进：移除NSP任务，动态掩码，更大批量
  - 训练：更多数据，更长时间
  - 性能：显著超越原始BERT

- **ALBERT**：
  - 创新：参数共享，句子顺序预测
  - 优势：更小的模型大小，更好的性能
  - 应用：资源受限场景

- **掩码策略变体**：
  - 静态vs动态掩码
  - 整词掩码vs标记掩码
  - 掩码比例优化

### 3.4 统一框架

- **T5**：
  - "文本到文本"框架：所有NLP任务转换为文本生成
  - 预训练任务：随机掩码连续文本段
  - 多任务学习：在预训练阶段引入多种任务

- **BART**：
  - 设计：结合BERT的双向编码器和GPT的自回归解码器
  - 预训练任务：文档破坏和重建
  - 损坏方式：标记掩码、句子置换、文档旋转等

- **MASS**：
  - 掩码序列到序列预训练
  - 同时学习编码和解码能力
  - 适用于理解和生成任务

## 4. 多模态自监督学习

### 4.1 视觉-语言对比学习

- **CLIP**：
  - 架构：独立的图像编码器和文本编码器
  - 预训练任务：最大化匹配图像-文本对的相似度
  - 数据：4亿图像-文本对
  - 零样本能力：可直接用于分类、检索等任务

- **ALIGN**：
  - 数据：更大规模的网络爬取图像-文本对(1.8B)
  - 噪声对比：处理嘈杂的网络数据
  - 性能：展示了数据规模的重要性

- **Florence**：
  - 统一视觉表示：从图像到视频、从识别到几何
  - 大规模预训练：9亿图像-文本对
  - 多粒度对比：像素、区域、图像级别

### 4.2 掩码多模态建模

- **ViLBERT/VisualBERT**：
  - 架构：扩展BERT处理视觉和语言输入
  - 预训练任务：跨模态掩码预测
  - 应用：视觉问答、视觉常识推理

- **ALBEF**：
  - 对齐前的对比学习
  - 多模态融合编码器
  - 图像-文本对比和掩码语言建模结合

- **BEiT-3**：
  - 统一视觉、视觉-语言和语言掩码预测
  - 共享多模态词汇表
  - 通用表示学习

### 4.3 生成式多模态学习

- **DALL-E/DALL-E 2**：
  - 任务：从文本描述生成图像
  - 方法：自回归或扩散模型
  - 预训练：大规模文本-图像对

- **Stable Diffusion**：
  - 潜在扩散模型
  - 文本条件图像生成
  - 开源影响

- **多模态生成模型的应用**：
  - 创意内容创作
  - 辅助设计
  - 数据增强

## 5. 自监督学习的技术挑战

### 5.1 表示崩溃问题

- **崩溃的类型**：
  - 常量崩溃：所有样本映射到同一表示
  - 维度崩溃：只使用表示空间的一小部分维度
  - 模态崩溃：多模态学习中偏向某一模态

- **防止崩溃的方法**：
  - 对比学习中的负样本
  - 动量编码器（如MoCo）
  - 预测器设计（如BYOL）
  - 聚类约束（如SwAV）
  - 停止梯度操作

- **理论分析**：
  - 信息最大化视角
  - 对称性破缺
  - 冗余表示

### 5.2 数据增强与不变性

- **常用数据增强技术**：
  - 视觉：裁剪、旋转、颜色抖动、模糊
  - 文本：回译、同义词替换、句法变换
  - 音频：时间扭曲、频谱增强、添加噪声

- **增强策略选择**：
  - 任务相关性：增强不应改变语义
  - 强度权衡：太弱无效，太强可能破坏语义
  - 组合策略：多种增强的协同效应

- **学习什么样的不变性**：
  - 语义不变性vs表面变化
  - 领域特定不变性
  - 可控不变性学习

### 5.3 评估与基准

- **线性评估协议**：
  - 冻结特征提取器
  - 只训练线性分类器
  - 优点：快速、简单、可比较
  - 局限：可能低估表示质量

- **迁移学习评估**：
  - 微调整个网络
  - 评估多个下游任务
  - 考虑样本效率（少样本性能）

- **公平比较的挑战**：
  - 架构差异
  - 计算预算差异
  - 预训练数据差异
  - 超参数敏感性

### 5.4 计算效率

- **大批量训练**：
  - 对比学习通常需要大批量
  - 内存效率技术：梯度累积、混合精度训练
  - 分布式训练策略

- **负样本处理**：
  - 内存库设计（如MoCo）
  - 批内负样本（如SimCLR）
  - 硬负样本挖掘

- **加速技术**：
  - 异步更新
  - 多阶段训练
  - 课程学习

## 6. 自监督学习的应用

### 6.1 计算机视觉应用

- **图像分类**：
  - 预训练-微调范式
  - 少样本学习
  - 长尾分布问题

- **目标检测与分割**：
  - 自监督预训练的迁移
  - 密集预测任务的适应
  - 与监督预训练的比较

- **视频理解**：
  - 时间一致性学习
  - 动作识别
  - 视频检索

### 6.2 自然语言处理应用

- **文本分类与序列标注**：
  - 情感分析
  - 命名实体识别
  - 文本蕴含

- **问答与摘要**：
  - 抽取式vs生成式
  - 知识密集型任务
  - 事实一致性

- **语言生成**：
  - 机器翻译
  - 对话系统
  - 文本创作

### 6.3 跨领域应用

- **医学影像**：
  - 有限标注数据问题
  - 模态特定预训练
  - 诊断辅助

- **遥感与地理空间**：
  - 多光谱数据
  - 时间序列分析
  - 变化检测

- **音频与语音**：
  - 语音识别
  - 音乐分析
  - 环境声音分类

## 7. 前沿研究方向

### 7.1 多任务自监督学习

- **任务设计与组合**：
  - 互补任务的选择
  - 任务权重平衡
  - 渐进式任务引入

- **统一框架**：
  - 多任务预训练目标
  - 共享表示学习
  - 任务间知识迁移

- **评估与分析**：
  - 任务对下游性能的贡献
  - 表示质量的多维度评估
  - 任务间干扰分析

### 7.2 自监督强化学习

- **内在动机**：
  - 好奇心驱动探索
  - 预测误差最大化
  - 状态覆盖最大化

- **表示学习**：
  - 状态表示学习
  - 动作表示学习
  - 目标条件策略

- **世界模型**：
  - 自监督动力学学习
  - 想象式规划
  - 模型不确定性估计

### 7.3 自监督学习理论

- **信息论视角**：
  - 互信息最大化
  - 信息瓶颈理论
  - 条件熵最小化

- **表示学习理论**：
  - 表示复杂度与泛化
  - 不变性与等变性
  - 可分离性与可转移性

- **优化理论**：
  - 对比损失的收敛性
  - 批量大小与学习动态
  - 预训练-微调范式的理论基础

### 7.4 基础模型与涌现能力

- **大规模自监督预训练**：
  - 缩放规律
  - 计算效率优化
  - 分布式训练技术

- **涌现能力**：
  - 能力涌现的临界规模
  - 指令遵循能力
  - 推理与规划能力

- **多模态基础模型**：
  - 统一表示空间
  - 跨模态推理
  - 模态对齐与融合

## 8. 实践指南与工具

### 8.1 自监督学习框架

- **PyTorch实现**：
  - VISSL：Facebook的视觉自监督学习库
  - LIGHTLY：简化自监督学习工作流
  - SOLO-LEARN：统一自监督方法实现

- **TensorFlow/JAX实现**：
  - SimCLR官方实现
  - BYOL-JAX
  - TF-Models自监督模块

- **预训练模型库**：
  - HuggingFace模型中心
  - PyTorch Hub
  - TensorFlow Hub

### 8.2 最佳实践与实现

#### 8.2.1 视觉自监督学习实现

##### SimCLR实现示例

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import transforms, models
from torch.utils.data import DataLoader

# 数据增强管道
class SimCLRDataTransform:
    def __init__(self, base_transform, size=224):
        self.base_transform = base_transform
        self.size = size
        
    def __call__(self, x):
        # 创建两个不同的增强视图
        transform = transforms.Compose([
            transforms.RandomResizedCrop(self.size),
            transforms.RandomHorizontalFlip(),
            transforms.RandomApply([
                transforms.ColorJitter(0.8, 0.8, 0.8, 0.2)
            ], p=0.8),
            transforms.RandomGrayscale(p=0.2),
            transforms.GaussianBlur(kernel_size=int(0.1 * self.size)),
            self.base_transform
        ])
        return transform(x), transform(x)

# 投影头网络
class ProjectionHead(nn.Module):
    def __init__(self, input_dim=2048, hidden_dim=2048, output_dim=128):
        super().__init__()
        self.projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim, output_dim)
        )
        
    def forward(self, x):
        return self.projection(x)

# SimCLR模型
class SimCLR(nn.Module):
    def __init__(self, base_encoder, projection_dim=128):
        super().__init__()
        
        # 基础编码器（如ResNet）
        self.encoder = base_encoder
        # 去掉分类头
        self.encoder.fc = nn.Identity()
        
        # 投影头
        self.projector = ProjectionHead(
            input_dim=base_encoder.inplanes,  # ResNet特征维度
            output_dim=projection_dim
        )
        
    def forward(self, x):
        # 从增强视图获取特征
        h = self.encoder(x)
        # 获取投影特征
        z = self.projector(h)
        return h, z

# NT-Xent（归一化温度缩放交叉熵）损失
class NTXentLoss(nn.Module):
    def __init__(self, temperature=0.5):
        super().__init__()
        self.temperature = temperature
        self.criterion = nn.CrossEntropyLoss()
        
    def forward(self, z_i, z_j):
        batch_size = z_i.size(0)
        
        # 计算相似度矩阵
        z = torch.cat([z_i, z_j], dim=0)
        z = F.normalize(z, dim=1)  # L2归一化
        similarity_matrix = torch.matmul(z, z.T) / self.temperature
        
        # 掩码掉自身相似度
        sim_i_j = torch.diag(similarity_matrix, batch_size)
        sim_j_i = torch.diag(similarity_matrix, -batch_size)
        
        positive_samples = torch.cat([sim_i_j, sim_j_i], dim=0).reshape(2 * batch_size, 1)
        
        # 创建正负样本掩码
        mask = (~torch.eye(2 * batch_size, 2 * batch_size, dtype=bool)).to(z.device)
        
        # 选择负样本
        negative_samples = similarity_matrix[mask].reshape(2 * batch_size, -1)
        
        # 合并正负样本
        logits = torch.cat([positive_samples, negative_samples], dim=1)
        labels = torch.zeros(2 * batch_size, dtype=torch.long).to(z.device)  # 正样本索引为0
        
        return self.criterion(logits, labels)

# 训练循环示例
def train_simclr(model, data_loader, optimizer, device, epoch):
    model.train()
    criterion = NTXentLoss()
    
    for images, _ in data_loader:
        # 获取增强后的两个视图
        x_i = images[0].to(device)
        x_j = images[1].to(device)
        
        # 前向传播
        _, z_i = model(x_i)
        _, z_j = model(x_j)
        
        # 计算对比损失
        loss = criterion(z_i, z_j)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
```

##### MAE (Masked Autoencoder)实现示例

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.vision_transformer import PatchEmbed, Block

class MaskedAutoencoder(nn.Module):
    def __init__(
        self, img_size=224, patch_size=16, in_chans=3,
        embed_dim=1024, depth=24, num_heads=16,
        decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
        mlp_ratio=4., norm_layer=nn.LayerNorm, mask_ratio=0.75
    ):
        super().__init__()
        
        # --------------------------------------------------------------------------
        # MAE编码器
        self.patch_embed = PatchEmbed(img_size, patch_size, in_chans, embed_dim)
        num_patches = self.patch_embed.num_patches
        
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim))
        
        self.blocks = nn.ModuleList([
            Block(embed_dim, num_heads, mlp_ratio, qkv_bias=True, norm_layer=norm_layer)
            for i in range(depth)])
        self.norm = norm_layer(embed_dim)
        
        # --------------------------------------------------------------------------
        # MAE解码器
        self.decoder_embed = nn.Linear(embed_dim, decoder_embed_dim, bias=True)
        self.mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, decoder_embed_dim))
        
        self.decoder_blocks = nn.ModuleList([
            Block(decoder_embed_dim, decoder_num_heads, mlp_ratio, qkv_bias=True, norm_layer=norm_layer)
            for i in range(decoder_depth)])
        self.decoder_norm = norm_layer(decoder_embed_dim)
        self.decoder_pred = nn.Linear(decoder_embed_dim, patch_size**2 * in_chans, bias=True)
        
        self.mask_ratio = mask_ratio
        
        self.initialize_weights()
        
    def initialize_weights(self):
        # 初始化参数
        nn.init.normal_(self.pos_embed, std=0.02)
        nn.init.normal_(self.decoder_pos_embed, std=0.02)
        
    def random_masking(self, x, mask_ratio):
        # 随机掩码生成
        N, L, D = x.shape  # batch, length, dim
        len_keep = int(L * (1 - mask_ratio))
        
        # 生成随机噪声用于随机排序
        noise = torch.rand(N, L, device=x.device)  
        
        # 排序噪声以获取掩码指数
        ids_shuffle = torch.argsort(noise, dim=1)
        ids_restore = torch.argsort(ids_shuffle, dim=1)
        
        # 保留的标记
        ids_keep = ids_shuffle[:, :len_keep]
        x_masked = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))
        
        # 生成掩码
        mask = torch.ones([N, L], device=x.device)
        mask[:, :len_keep] = 0
        # 恢复原始顺序的掩码
        mask = torch.gather(mask, dim=1, index=ids_restore)
        
        return x_masked, mask, ids_restore
        
    def forward_encoder(self, x, mask_ratio):
        # 图像嵌入
        x = self.patch_embed(x)
        
        # 添加位置编码
        x = x + self.pos_embed[:, 1:, :]
        
        # 掩码: 随机删除一些标记
        x, mask, ids_restore = self.random_masking(x, mask_ratio)
        
        # 添加class token
        cls_token = self.cls_token + self.pos_embed[:, :1, :]
        cls_tokens = cls_token.expand(x.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)
        
        # 应用Transformer块
        for blk in self.blocks:
            x = blk(x)
        x = self.norm(x)
        
        return x, mask, ids_restore
        
    def forward_decoder(self, x, ids_restore):
        # 将编码器特征嵌入到解码器维度
        x = self.decoder_embed(x)
        
        # 添加掩码标记
        mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] - 1, 1)
        cls_token = x[:, :1, :]  # 分离类标记
        x_ = x[:, 1:, :]  # 删除类标记
        
        # 将掩码标记添加回来
        x_ = torch.cat([x_, mask_tokens], dim=1)
        
        # 恢复原始顺序
        x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]))
        
        # 添加位置编码
        x = torch.cat([cls_token, x_], dim=1)
        x = x + self.decoder_pos_embed
        
        # 应用Transformer块
        for blk in self.decoder_blocks:
            x = blk(x)
        x = self.decoder_norm(x)
        
        # 预测像素
        x = self.decoder_pred(x)
        
        # 移除class token
        x = x[:, 1:, :]
        
        return x
        
    def forward(self, imgs):
        # 编码
        latent, mask, ids_restore = self.forward_encoder(imgs, self.mask_ratio)
        
        # 解码
        pred = self.forward_decoder(latent, ids_restore)
        
        # 计算损失
        loss = (pred - imgs) ** 2
        loss = loss.mean(dim=-1)  # [N, L]
        
        # 只计算掩码位置的损失
        loss = (loss * mask).sum() / mask.sum()
        
        return loss
```

#### 8.2.2 自然语言处理自监督学习实现

##### 简易版BERT掩码语言模型实现

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import BertTokenizer, BertModel, BertConfig

class SimpleMaskedLanguageModel(nn.Module):
    def __init__(self, vocab_size=30522, hidden_size=768, num_hidden_layers=6, 
                 num_attention_heads=12, max_position_embeddings=512):
        super().__init__()
        # 使用BertConfig配置一个较小的BERT模型
        config = BertConfig(
            vocab_size=vocab_size,
            hidden_size=hidden_size,
            num_hidden_layers=num_hidden_layers,
            num_attention_heads=num_attention_heads,
            intermediate_size=hidden_size*4,
            max_position_embeddings=max_position_embeddings
        )
        
        # 创建BERT模型
        self.bert = BertModel(config)
        
        # 添加MLM头
        self.mlm_head = nn.Linear(hidden_size, vocab_size)
        
    def forward(self, input_ids, attention_mask=None, token_type_ids=None, 
                masked_lm_labels=None, position_ids=None):
        
        # 获取BERT输出
        outputs = self.bert(
            input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            position_ids=position_ids,
            output_hidden_states=True
        )
        
        # 获取序列输出
        sequence_output = outputs.last_hidden_state
        
        # 预测掩码标记
        prediction_scores = self.mlm_head(sequence_output)
        
        # 如果提供了标签，计算损失
        loss = None
        if masked_lm_labels is not None:
            loss_fct = nn.CrossEntropyLoss()  # 忽略填充标记[-100]
            masked_lm_loss = loss_fct(
                prediction_scores.view(-1, self.config.vocab_size),
                masked_lm_labels.view(-1)
            )
            loss = masked_lm_loss
            
        return loss, prediction_scores

# 掩码处理函数
def create_masked_input(tokenizer, text, mlm_probability=0.15):
    # 将文本标记化
    inputs = tokenizer(text, return_tensors="pt", padding=True, truncation=True)
    input_ids = inputs["input_ids"].clone()
    labels = input_ids.clone()
    
    # 创建掩码
    probability_matrix = torch.full(labels.shape, mlm_probability)
    # 不掩码特殊标记
    special_tokens_mask = [
        tokenizer.get_special_tokens_mask(val, already_has_special_tokens=True)
        for val in labels.tolist()
    ]
    special_tokens_mask = torch.tensor(special_tokens_mask, dtype=torch.bool)
    probability_matrix.masked_fill_(special_tokens_mask, value=0.0)
    
    # 随机掩码
    masked_indices = torch.bernoulli(probability_matrix).bool()
    labels[~masked_indices] = -100  # 只为掩码标记计算损失
    
    # 掩码标记的80%用[MASK]替换
    indices_replaced = torch.bernoulli(torch.full(labels.shape, 0.8)).bool() & masked_indices
    input_ids[indices_replaced] = tokenizer.convert_tokens_to_ids(tokenizer.mask_token)
    
    # 掩码标记的10%用随机标记替换
    indices_random = torch.bernoulli(torch.full(labels.shape, 0.5)).bool() & masked_indices & ~indices_replaced
    random_words = torch.randint(len(tokenizer), labels.shape, dtype=torch.long)
    input_ids[indices_random] = random_words[indices_random]
    
    # 10%保持不变
    
    return input_ids, labels, inputs["attention_mask"]

# 训练示例
def train_mlm(model, tokenizer, texts, optimizer, device, epochs=3):
    model.train()
    
    for epoch in range(epochs):
        total_loss = 0
        for text in texts:
            # 创建掩码输入
            input_ids, labels, attention_mask = create_masked_input(tokenizer, text)
            input_ids = input_ids.to(device)
            labels = labels.to(device)
            attention_mask = attention_mask.to(device)
            
            # 前向传播
            loss, _ = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                masked_lm_labels=labels
            )
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
```

#### 8.2.3 多模态自监督学习实现

##### 简化版CLIP实现

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models
from transformers import BertModel, BertTokenizer

class ImageEncoder(nn.Module):
    def __init__(self, embed_dim=512):
        super().__init__()
        # 使用预训练的ResNet作为图像编码器骨干
        self.backbone = models.resnet50(pretrained=True)
        # 替换最后的fc层
        self.backbone.fc = nn.Linear(self.backbone.fc.in_features, embed_dim)
        
    def forward(self, x):
        return self.backbone(x)

class TextEncoder(nn.Module):
    def __init__(self, embed_dim=512):
        super().__init__()
        # 使用BERT作为文本编码器
        self.bert = BertModel.from_pretrained('bert-base-uncased')
        # 添加投影层
        self.projection = nn.Linear(self.bert.config.hidden_size, embed_dim)
        
    def forward(self, input_ids, attention_mask):
        # 获取[CLS]标记的输出作为文本表示
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        cls_output = outputs.last_hidden_state[:, 0, :]
        return self.projection(cls_output)

class SimpleCLIP(nn.Module):
    def __init__(self, embed_dim=512, temperature=0.07):
        super().__init__()
        self.image_encoder = ImageEncoder(embed_dim)
        self.text_encoder = TextEncoder(embed_dim)
        self.temperature = temperature
        self.logit_scale = nn.Parameter(torch.ones([]) * np.log(1 / temperature))
        
    def forward(self, images, input_ids, attention_mask):
        # 获取图像和文本的嵌入
        image_features = self.image_encoder(images)
        text_features = self.text_encoder(input_ids, attention_mask)
        
        # L2归一化
        image_features = F.normalize(image_features, dim=1)
        text_features = F.normalize(text_features, dim=1)
        
        # 计算余弦相似度
        logit_scale = self.logit_scale.exp()
        logits_per_image = logit_scale * image_features @ text_features.t()
        logits_per_text = logits_per_image.t()
        
        return logits_per_image, logits_per_text

# 对比损失计算
def clip_loss(logits_per_image, logits_per_text):
    # 创建标签 - 对角线表示匹配的图像-文本对
    batch_size = logits_per_image.shape[0]
    labels = torch.arange(batch_size).to(logits_per_image.device)
    
    # 计算图像到文本和文本到图像的损失
    image_loss = F.cross_entropy(logits_per_image, labels)
    text_loss = F.cross_entropy(logits_per_text, labels)
    
    # 总损失是两个方向损失的平均
    total_loss = (image_loss + text_loss) / 2
    return total_loss

# 训练循环示例
def train_clip(model, data_loader, optimizer, device, epoch):
    model.train()
    
    for images, texts in data_loader:
        # 将图像移到设备
        images = images.to(device)
        
        # 标记化文本
        tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        text_tokens = tokenizer(
            texts, 
            return_tensors="pt", 
            padding=True, 
            truncation=True, 
            max_length=77
        )
        input_ids = text_tokens["input_ids"].to(device)
        attention_mask = text_tokens["attention_mask"].to(device)
        
        # 前向传播
        logits_per_image, logits_per_text = model(images, input_ids, attention_mask)
        
        # 计算损失
        loss = clip_loss(logits_per_image, logits_per_text)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
```

#### 8.2.4 实际应用示例

##### 使用自监督预训练模型进行迁移学习

```python
import torch
import torch.nn as nn
from torchvision import models, transforms, datasets
from torch.utils.data import DataLoader

# 加载预训练的SimCLR模型
def load_pretrained_simclr(checkpoint_path, arch='resnet50'):
    # 创建基础模型
    if arch == 'resnet50':
        model = models.resnet50(pretrained=False)
        model.fc = nn.Identity()  # 移除分类头
    
    # 加载预训练权重
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 过滤出只有编码器的权重
    encoder_state_dict = {}
    for k, v in checkpoint['state_dict'].items():
        if k.startswith('encoder.'):
            # 移除'encoder.'前缀
            encoder_state_dict[k[8:]] = v
    
    # 加载权重
    model.load_state_dict(encoder_state_dict, strict=False)
    
    return model

# 为下游任务创建分类器
class DownstreamClassifier(nn.Module):
    def __init__(self, backbone, num_classes):
        super().__init__()
        self.backbone = backbone
        # 冻结预训练骨干网络
        for param in self.backbone.parameters():
            param.requires_grad = False
        
        # 添加分类头
        self.classifier = nn.Sequential(
            nn.Linear(2048, 512),  # 假设ResNet50的特征维度为2048
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, num_classes)
        )
        
    def forward(self, x):
        features = self.backbone(x)
        return self.classifier(features)

# 微调示例
def finetune_model(model, train_loader, val_loader, device, epochs=10):
    # 设置优化器 - 只更新分类器参数
    optimizer = torch.optim.Adam(model.classifier.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    best_acc = 0.0
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        for images, labels in train_loader:
            images = images.to(device)
            labels = labels.to(device)
            
            # 前向传播
            outputs = model(images)
            loss = criterion(outputs, labels)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        
        # 验证阶段
        model.eval()
        correct = 0
        total = 0
        with torch.no_grad():
            for images, labels in val_loader:
                images = images.to(device)
                labels = labels.to(device)
                
                outputs = model(images)
                _, predicted = torch.max(outputs.data, 1)
                
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        
        accuracy = correct / total
        print(f'Epoch {epoch+1}/{epochs}, Validation Accuracy: {accuracy:.4f}')
        
        # 保存最佳模型
        if accuracy > best_acc:
            best_acc = accuracy
            torch.save(model.state_dict(), 'best_model.pth')

# 数据加载示例
def prepare_data(data_dir, batch_size=32):
    # 数据转换
    transform = transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 加载数据集
    train_dataset = datasets.ImageFolder(
        root=data_dir + '/train',
        transform=transform
    )
    
    val_dataset = datasets.ImageFolder(
        root=data_dir + '/val',
        transform=transform
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4
    )
    
    return train_loader, val_loader, len(train_dataset.classes)

# 主函数
def main(pretrained_path, data_dir):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载预训练模型
    backbone = load_pretrained_simclr(pretrained_path)
    
    # 准备数据
    train_loader, val_loader, num_classes = prepare_data(data_dir)
    
    # 创建下游任务分类器
    model = DownstreamClassifier(backbone, num_classes).to(device)
    
    # 微调模型
    finetune_model(model, train_loader, val_loader, device)
    
    print("Training complete!")
```

#### 8.2.5 自监督学习框架使用指南

##### 使用VISSL框架训练SimCLR

```python
# 安装VISSL
# pip install vissl

import yaml
from vissl.utils.hydra_config import compose_hydra_configuration, convert_to_attrdict
from vissl.utils.checkpoint import CheckpointLoader
from vissl.models import build_model
from vissl.data import build_dataset
from vissl.trainer import SelfSupervisionTrainer
from vissl.utils.hydra_config import AttrDict
import torch

# 创建配置
def get_config():
    cfg = AttrDict()
    
    # 数据配置
    cfg.DATA = AttrDict()
    cfg.DATA.TRAIN = ["imagenet1k_folder"]
    cfg.DATA.TRAIN_DATASOURCES = AttrDict()
    cfg.DATA.TRAIN_DATASOURCES.imagenet1k_folder = AttrDict()
    cfg.DATA.TRAIN_DATASOURCES.imagenet1k_folder.TRANSFORMS = [
        "RandomResizedCrop",
        "RandomHorizontalFlip",
        "ColorJitter",
        "ToTensor",
        "Normalize",
    ]
    cfg.DATA.TRAIN_DATASOURCES.imagenet1k_folder.PATH = "/path/to/imagenet"
    cfg.DATA.TRAIN_DATASOURCES.imagenet1k_folder.LABEL_SOURCES = [["disk_folder"]]
    cfg.DATA.TRAIN_DATASOURCES.imagenet1k_folder.DISK_FOLDER_DATASET_CONFIG = AttrDict()
    cfg.DATA.TRAIN_DATASOURCES.imagenet1k_folder.DISK_FOLDER_DATASET_CONFIG.USE_PYTORCH_DATASET = True
    
    # 模型配置
    cfg.MODEL = AttrDict()
    cfg.MODEL.TRUNK = AttrDict()
    cfg.MODEL.TRUNK.NAME = "resnet"
    cfg.MODEL.TRUNK.TRUNK_PARAMS = AttrDict()
    cfg.MODEL.TRUNK.TRUNK_PARAMS.RESNETS = AttrDict()
    cfg.MODEL.TRUNK.TRUNK_PARAMS.RESNETS.DEPTH = 50
    
    cfg.MODEL.HEAD = AttrDict()
    cfg.MODEL.HEAD.PARAMS = [["mlp", {"dims": [2048, 2048, 128]}]]
    
    # 损失函数配置
    cfg.LOSS = AttrDict()
    cfg.LOSS.name = "simclr_info_nce_loss"
    cfg.LOSS.simclr_info_nce_loss = AttrDict()
    cfg.LOSS.simclr_info_nce_loss.temperature = 0.1
    
    # 训练配置
    cfg.OPTIMIZER = AttrDict()
    cfg.OPTIMIZER.name = "sgd"
    cfg.OPTIMIZER.weight_decay = 1e-4
    cfg.OPTIMIZER.momentum = 0.9
    cfg.OPTIMIZER.nesterov = False
    cfg.OPTIMIZER.regularize_bn = False
    cfg.OPTIMIZER.num_epochs = 100
    cfg.OPTIMIZER.param_schedulers = AttrDict()
    cfg.OPTIMIZER.param_schedulers.lr = AttrDict()
    cfg.OPTIMIZER.param_schedulers.lr.name = "cosine"
    cfg.OPTIMIZER.param_schedulers.lr.start_value = 0.3
    cfg.OPTIMIZER.param_schedulers.lr.end_value = 0.0
    
    # 数据加载配置
    cfg.DATA_LOADER = AttrDict()
    cfg.DATA_LOADER.TRAIN_BATCHSIZE_PER_REPLICA = 256
    cfg.DATA_LOADER.NUM_TRAIN_SAMPLES = 1281167  # ImageNet样本数量
    
    # 分布式训练配置
    cfg.DISTRIBUTED = AttrDict()
    cfg.DISTRIBUTED.NUM_NODES = 1
    cfg.DISTRIBUTED.NUM_PROC_PER_NODE = 8
    
    return cfg

# 创建和训练模型
def train_simclr_with_vissl():
    # 获取配置
    config = get_config()
    
    # 构建模型
    model = build_model(config["MODEL"])
    
    # 构建数据集
    datasets = build_dataset(config)
    
    # 创建训练器
    trainer = SelfSupervisionTrainer(
        config=config,
        model=model,
        datasets=datasets,
        optimizer=None,  # 让trainer创建优化器
        device=torch.device("cuda" if torch.cuda.is_available() else "cpu"),
    )
    
    # 训练模型
    trainer.train()
    
    # 保存模型
    trainer.save_checkpoint(checkpoint_folder="./checkpoints")
```

##### 使用PyTorch Lightning实现自监督学习

```python
import pytorch_lightning as pl
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torchvision import models, transforms, datasets

class SimCLRLightning(pl.LightningModule):
    def __init__(self, temperature=0.5, lr=1e-3):
        super().__init__()
        self.save_hyperparameters()
        
        # 创建编码器
        self.encoder = models.resnet50(pretrained=False)
        self.encoder.fc = nn.Identity()
        
        # 投影头
        self.projector = nn.Sequential(
            nn.Linear(2048, 2048),
            nn.ReLU(),
            nn.Linear(2048, 128)
        )
        
        self.temperature = temperature
        
    def forward(self, x):
        h = self.encoder(x)
        z = self.projector(h)
        return h, z
    
    def training_step(self, batch, batch_idx):
        x_i = batch[0][0]  # 第一个增强视图
        x_j = batch[0][1]  # 第二个增强视图
        
        # 获取表示
        _, z_i = self(x_i)
        _, z_j = self(x_j)
        
        # 归一化
        z_i = F.normalize(z_i, dim=1)
        z_j = F.normalize(z_j, dim=1)
        
        # 计算相似度矩阵
        representations = torch.cat([z_i, z_j], dim=0)
        similarity_matrix = F.cosine_similarity(
            representations.unsqueeze(1), representations.unsqueeze(0), dim=2
        ) / self.temperature
        
        # 掩码自身相似度
        batch_size = z_i.shape[0]
        mask = (~torch.eye(2 * batch_size, device=self.device, dtype=bool)).float()
        similarity_matrix = similarity_matrix * mask
        
        # 创建标签 - 匹配正例
        positives = torch.zeros(2 * batch_size).to(self.device).long()
        for i in range(batch_size):
            positives[i] = batch_size + i
            positives[batch_size + i] = i
        
        # 计算损失
        loss = F.cross_entropy(similarity_matrix, positives)
        
        # 记录指标
        self.log('train_loss', loss)
        
        return loss
    
    def configure_optimizers(self):
        optimizer = torch.optim.Adam(self.parameters(), lr=self.hparams.lr)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=100)
        return [optimizer], [scheduler]

# 数据增强类
class SimCLRDataTransform:
    def __init__(self):
        self.train_transform = transforms.Compose([
            transforms.RandomResizedCrop(224),
            transforms.RandomHorizontalFlip(),
            transforms.RandomApply([transforms.ColorJitter(0.8, 0.8, 0.8, 0.2)], p=0.8),
            transforms.RandomGrayscale(p=0.2),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def __call__(self, x):
        return self.train_transform(x), self.train_transform(x)

# 运行训练
def train_with_lightning(data_path):
    # 创建数据集
    train_dataset = datasets.ImageFolder(
        root=data_path,
        transform=SimCLRDataTransform()
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=128,
        shuffle=True,
        num_workers=8,
        pin_memory=True
    )
    
    # 创建模型
    model = SimCLRLightning()
    
    # 创建训练器
    trainer = pl.Trainer(
        max_epochs=100,
        gpus=1 if torch.cuda.is_available() else 0,
        precision=16,  # 混合精度训练
        log_every_n_steps=10
    )
    
    # 训练模型
    trainer.fit(model, train_loader)
    
    # 保存模型
    trainer.save_checkpoint("simclr_lightning.ckpt")
```

这些实现示例提供了各种自监督学习方法的实用代码框架。在实际应用中，您可能需要根据具体任务和数据特点进行调整，包括更改网络架构、优化超参数和数据处理方式。通过这些实现，您可以快速开始自监督学习实验，并将学到的表示应用于下游任务。 