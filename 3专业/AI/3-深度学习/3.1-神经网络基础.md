# 神经网络基础

## 概述
神经网络是深度学习的基础，模拟人脑神经元的连接方式处理信息。

## 基本组件
- 神经元与激活函数
  - Sigmoid
  - Tanh
  - ReLU及其变体
  - Softmax
- 层
  - 输入层
  - 隐藏层
  - 输出层
- 连接与权重
- 偏置

## 前向传播与反向传播
- 计算图
- 链式法则
- 梯度计算
- 误差传播

## 损失函数
- 均方误差(MSE)
- 交叉熵
- Hinge Loss
- Focal Loss
- 对比损失

## 优化算法
- 梯度下降
- 随机梯度下降(SGD)
- Mini-batch SGD
- 动量法
- AdaGrad
- RMSProp
- Adam及其变体

## 正则化技术
- L1/L2正则化
- Dropout
- 批量归一化
- 层归一化
- 提前停止
- 数据增强

## 初始化方法
- 零初始化
- 随机初始化
- Xavier/Glorot初始化
- He初始化
- 正交初始化 