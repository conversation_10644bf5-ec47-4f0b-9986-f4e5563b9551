# 卷积神经网络(CNN)

## 概述
卷积神经网络是一种特别适用于处理网格结构数据(如图像)的深度学习架构。

## 基本组件
- 卷积层
  - 卷积核/过滤器
  - 步长
  - 填充
  - 空洞卷积
- 池化层
  - 最大池化
  - 平均池化
  - 全局池化
- 全连接层
- 批归一化

## 经典CNN架构
- LeNet-5
- AlexNet
- VGG
- GoogLeNet/Inception
- ResNet
- DenseNet
- MobileNet
- EfficientNet

## 高级技术
- 残差连接
- 跳跃连接
- 注意力机制
- 空间变换网络
- 可分离卷积

## 应用场景
- 图像分类
- 目标检测
- 语义分割
- 实例分割
- 图像生成
- 视频分析
- 医学图像处理

## 训练技巧
- 数据增强
- 学习率调度
- 迁移学习
- 知识蒸馏
- 网络量化与剪枝 