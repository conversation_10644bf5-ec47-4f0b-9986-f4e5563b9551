# 5.1 CV基础

## 概述
计算机视觉(Computer Vision, CV)是人工智能的重要分支，旨在使计算机能够理解和解释视觉信息，模拟人类视觉系统的功能。从医疗诊断到自动驾驶，计算机视觉技术已广泛应用于各个领域。

## 图像处理基础

- **图像表示**：
  - 数字图像以像素矩阵形式存储
  - 灰度图：单通道，像素值表示亮度(0-255)
  - 彩色图：多通道，如RGB(红绿蓝)三通道
  - 示例：1080p图像为1920×1080像素矩阵

- **颜色空间**：
  - **RGB**：加色模型，适合显示设备
  - **HSV/HSL**：色调-饱和度-明度/亮度，更符合人类感知
  - **YUV/YCbCr**：亮度与色度分离，用于视频编码
  - **Lab**：感知均匀，设计用于接近人眼感知
  - 应用：颜色分割、肤色检测、照明不变性处理

- **图像滤波**：
  - **平滑滤波**：
    - 均值滤波：简单平均，快速但会模糊边缘
    - 高斯滤波：加权平均，保留更多细节
    - 中值滤波：用中值替代，去除椒盐噪声
  - **锐化滤波**：
    - 拉普拉斯算子：增强边缘和细节
  - 应用：噪声去除、图像增强、预处理

- **边缘检测**：
  - **原理**：检测图像中亮度急剧变化的区域
  - **方法**：
    - Sobel算子：计算水平和垂直梯度
    - Canny边缘检测：多阶段算法，包括高斯平滑、梯度计算、非极大值抑制和滞后阈值
  - **公式**：Sobel梯度幅值 = √(Gx² + Gy²)
  - 应用：物体轮廓提取、形状分析、特征点检测前处理

## 特征提取

- **SIFT (尺度不变特征变换)**：
  - **原理**：在不同尺度空间检测并描述局部特征
  - **优势**：对旋转、缩放、亮度变化具有不变性
  - **步骤**：构建尺度空间→检测极值点→精确定位→分配方向→生成描述符
  - **应用**：物体识别、全景拼接、3D重建

- **HOG (方向梯度直方图)**：
  - **原理**：统计图像局部区域梯度方向分布
  - **步骤**：计算梯度→构建细胞直方图→块归一化→特征向量
  - **应用**：行人检测、物体识别、姿态估计

- **角点检测**：
  - **Harris角点**：基于局部窗口移动时强度变化
  - **特点**：对旋转不变但对尺度变化敏感
  - **FAST角点**：比较中心点与周围像素，速度快
  - **应用**：特征跟踪、动作识别、相机定位

## 传统视觉方法

- **图像分割**：
  - **阈值分割**：基于像素强度分离前景背景
  - **区域生长**：从种子点扩展相似区域
  - **分水岭算法**：将图像视为地形图进行分割
  - **应用案例**：医学图像器官分割、卫星图像地形分析

- **对象检测**：
  - **Viola-Jones算法**：级联分类器+Haar特征，经典人脸检测方法
  - **HOG+SVM**：结合HOG特征和支持向量机分类器
  - **滑动窗口**：在不同位置和尺度检测目标
  - **应用**：人脸/车辆/行人检测，产品质检

- **光流估计**：
  - **原理**：计算图像序列中像素位移场
  - **方法**：
    - Lucas-Kanade：局部光流假设局部运动一致
    - Horn-Schunck：全局方法加入平滑约束
  - **应用**：运动跟踪、视频稳定、动作识别

- **立体视觉**：
  - **原理**：利用两个或多个相机视角差异计算深度
  - **步骤**：相机标定→特征匹配→视差计算→深度恢复
  - **应用**：3D重建、距离测量、机器人导航

## 深度学习应用

- **CNN基础架构**：
  - **卷积层**：提取局部特征
  - **池化层**：降维、提高鲁棒性
  - **全连接层**：高级特征集成和分类
  - **经典网络**：LeNet, AlexNet, VGG, ResNet, Inception

- **图像分类**：
  - **任务**：识别图像中的主要对象类别
  - **技术**：CNN架构，迁移学习，数据增强
  - **案例**：皮肤病变分类、植物识别、产品质检

- **目标检测**：
  - **技术路线**：
    - 一阶段检测器(YOLO, SSD)：速度快，适合实时应用
    - 二阶段检测器(R-CNN系列)：精度高，计算量大
  - **案例**：自动驾驶场景理解、安防监控、零售库存管理

- **语义分割**：
  - **任务**：像素级分类，标记每个像素所属类别
  - **架构**：FCN, U-Net, DeepLab, SegNet
  - **应用**：医疗图像分割、卫星图像分析、增强现实

## 实际应用案例

1. **自动驾驶视觉系统**：
   - 任务：车道线检测、障碍物识别、交通标志识别
   - 技术：语义分割+目标检测+深度估计
   - 挑战：恶劣天气条件、实时性要求、安全性保障

2. **医疗影像分析**：
   - 任务：肿瘤检测、器官分割、病变分类
   - 示例：利用CNN检测X光片中的肺炎特征，准确率超过专业放射科医生
   - 价值：辅助诊断、减轻医生工作量、提高诊断速度

3. **智能零售**：
   - 无人商店：商品识别、客流分析、行为理解
   - 智能货架：库存管理、缺货检测
   - 技术组合：目标检测+姿态估计+行为分析

4. **增强现实**：
   - SLAM技术建立环境3D地图
   - 目标检测识别现实物体
   - 姿态估计实现虚拟物体正确叠加
   - 案例：IKEA家具AR试用、Pokemon Go游戏

## 计算机视觉评估指标

- **分类任务**：
  - 准确率(Accuracy)、精确率(Precision)、召回率(Recall)、F1分数
  - 混淆矩阵分析错误类型

- **目标检测**：
  - 交并比(IoU)：预测边界框与真实框重叠度
  - 平均精度(AP)：不同IoU阈值下的精度平均值
  - mAP：所有类别AP的平均

- **分割任务**：
  - 平均交并比(mIoU)：预测区域与真实区域重叠度
  - 像素精度：正确分类的像素比例

## 开发工具与资源

- **库与框架**：
  - OpenCV：开源计算机视觉库，提供广泛的图像处理和视觉算法
  - TensorFlow/PyTorch：深度学习框架，用于构建和训练视觉模型
  - CUDA/cuDNN：GPU加速计算

- **数据集**：
  - ImageNet：包含1400多万张分类图像
  - COCO：用于目标检测、分割和关键点检测
  - Cityscapes：城市场景语义理解

- **入门建议**：
  - 掌握图像处理基础(OpenCV)
  - 理解CNN原理及架构
  - 从简单任务(如图像分类)开始
  - 利用迁移学习解决小数据问题 