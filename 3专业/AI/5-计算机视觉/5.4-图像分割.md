# 5.4 图像分割

图像分割（Image Segmentation）是计算机视觉中的一项基础任务，旨在将图像划分为多个有意义的区域或对象。本文档将系统介绍图像分割的基本概念、主要方法、评估指标以及应用场景。

## 目录
- [1. 图像分割概述](#1-图像分割概述)
- [2. 图像分割的发展历程](#2-图像分割的发展历程)
- [3. 图像分割的主要方法](#3-图像分割的主要方法)
- [4. 图像分割的关键技术](#4-图像分割的关键技术)
- [5. 图像分割评估指标](#5-图像分割评估指标)
- [6. 图像分割数据集](#6-图像分割数据集)
- [7. 图像分割应用场景](#7-图像分割应用场景)
- [8. 图像分割的优化技术](#8-图像分割的优化技术)
- [9. 代码实现示例](#9-代码实现示例)
- [10. 最新研究进展](#10-最新研究进展)
- [11. 总结](#11-总结)
- [参考资料](#参考资料)

## 1. 图像分割概述

### 1.1 定义与目标

图像分割是将数字图像分割成多个图像子区域（像素集合）的过程，目的是简化或改变图像的表示，使其更易于分析和理解。其主要目标包括：

- 将图像划分为有意义的区域或对象
- 为每个像素分配类别标签
- 提取图像中的前景对象
- 理解场景的语义结构

### 1.2 图像分割的类型

#### 1.2.1 语义分割（Semantic Segmentation）

- 为图像中的每个像素分配一个类别标签
- 不区分同一类别的不同实例
- 例如：将图像中所有"人"像素标记为同一类别

#### 1.2.2 实例分割（Instance Segmentation）

- 检测并分割图像中的每个对象实例
- 区分同一类别的不同实例
- 例如：将图像中的每个"人"作为单独的实例进行标记

#### 1.2.3 全景分割（Panoptic Segmentation）

- 结合语义分割和实例分割的优点
- 为每个像素分配类别标签和实例ID
- 同时处理"可数"（如人、车）和"不可数"（如天空、道路）对象

#### 1.2.4 部分分割（Part Segmentation）

- 进一步将物体分割成有意义的部分
- 例如：将"人"分割为头部、躯干、四肢等
- 用于精细级别的理解和分析

### 1.3 图像分割的挑战

- **边界模糊**：对象边界不清晰或渐变
- **复杂背景**：背景与前景相似或复杂
- **尺度变化**：对象大小差异显著
- **遮挡问题**：对象被部分遮挡
- **类内变化**：同一类别的外观差异大
- **光照变化**：不同光照条件下的外观变化
- **计算复杂性**：高分辨率图像的处理效率
- **域迁移**：模型从一个场景迁移到另一个场景的能力
- **长尾分布**：罕见类别的识别和分割

## 2. 图像分割的发展历程

### 2.1 传统方法时期（1970s-2000s）

- **阈值分割**：基于像素强度的简单分割
- **边缘检测**：Canny、Sobel等边缘检测器
- **区域生长**：从种子点扩展区域
- **分水岭算法**：基于地形学的分割
- **图割算法**：如Graph Cuts、GrabCut
- **活动轮廓模型**：如Snake模型、Level Set方法
- **均值漂移**：基于密度估计的聚类方法

### 2.2 机器学习时期（2000s-2014）

- **超像素方法**：SLIC、Quickshift等
- **条件随机场（CRF）**：结构化预测模型
- **随机森林**：基于特征的分类器
- **支持向量机（SVM）**：结合手工特征的分类

### 2.3 深度学习时期（2015至今）

- **FCN**（2015）：首个端到端的全卷积网络
- **U-Net**（2015）：医学图像分割的里程碑
- **SegNet**（2016）：编码器-解码器架构
- **DeepLab系列**（2015-2018）：空洞卷积和ASPP
- **PSPNet**（2017）：金字塔池化模块
- **Mask R-CNN**（2017）：实例分割的代表作
- **Transformer-based**（2020+）：SETR、SegFormer等
- **SAM**（2023）：分割任何物体的通用模型
- **Grounding DINO + SAM**（2023）：开放词汇引导分割

## 3. 图像分割的主要方法

### 3.1 语义分割方法

#### 3.1.1 全卷积网络（FCN）

- 将分类网络的全连接层替换为卷积层
- 使用转置卷积进行上采样
- 融合不同层次的特征图
- 端到端训练

核心创新：
- 全卷积设计，适应任意尺寸输入
- 跳跃结构，结合浅层细节和深层语义
- 像素级端到端训练

#### 3.1.2 编码器-解码器架构

**U-Net**：
- U形对称结构
- 跳跃连接融合高低层特征
- 适用于医学图像分割

**SegNet**：
- 编码器-解码器结构
- 记录最大池化索引
- 使用池化索引进行上采样

**FPN (特征金字塔网络)**：
- 自顶向下的特征融合
- 不同尺度的语义信息整合
- 高效处理多尺度对象

#### 3.1.3 多尺度特征融合

**PSPNet（Pyramid Scene Parsing Network）**：
- 金字塔池化模块（PPM）
- 多尺度上下文信息融合
- 全局场景先验

**DeepLab系列**：
- 空洞卷积（膨胀卷积）
- 空洞空间金字塔池化（ASPP）
- 条件随机场（CRF）后处理（v1/v2）
- 深度可分离卷积（v3/v3+）
- Xception骨干网络（v3+）

#### 3.1.4 基于Transformer的方法

**SETR（Segmentation Transformer）**：
- 纯Transformer编码器
- 多层次特征提取
- 渐进式上采样解码器

**SegFormer**：
- 轻量级MiT（Mix Transformer）编码器
- 多尺度特征融合
- 简单的MLP解码头

**Mask2Former**：
- 统一掩码注意力机制
- 解决多种分割任务
- 基于查询的掩码预测

### 3.2 实例分割方法

#### 3.2.1 基于检测的方法

**Mask R-CNN**：
- 扩展Faster R-CNN
- 添加掩码预测分支
- RoIAlign替代RoI池化
- 并行的边界框回归、分类和掩码预测

**YOLACT**：
- 实时实例分割
- 原型掩码和掩码系数
- 线性组合生成实例掩码

**SOLOv2**：
- 无锚框、无提议的实例分割
- 动态卷积生成掩码
- 矩阵NMS加速推理

#### 3.2.2 基于分割的方法

**实例嵌入**：
- 学习像素嵌入向量
- 相同实例的像素具有相似的嵌入
- 聚类生成实例分割

**SOLO（Segmenting Objects by LOcations）**：
- 将实例分割问题转化为位置分类问题
- 网格化图像空间
- 预测每个网格单元中的实例掩码

**PointRend**：
- 自适应点级特征提取
- 基于不确定性的迭代细化
- 高效生成高分辨率边界

### 3.3 全景分割方法

#### 3.3.1 两阶段方法

**Panoptic FPN**：
- 结合Mask R-CNN和语义分割头
- 共享特征提取骨干网络
- 后处理合并两种分割结果

#### 3.3.2 统一架构

**UPSNet（Unified Panoptic Segmentation Network）**：
- 单一网络同时预测实例和语义分割
- 引入未知类别预测
- 参数化逻辑融合模块

**Panoptic-DeepLab**：
- 自底向上的实例分割
- 中心点检测和偏移预测
- 与语义分割结合

**K-Net**：
- 统一的内核学习框架
- 动态卷积生成掩码
- 迭代式内核更新

### 3.4 基于基础模型的方法

**Segment Anything Model (SAM)**：
- 大规模预训练通用分割模型
- 可接受多种提示（点、框、掩码、文本）
- 零样本迁移到新领域能力强
- 11亿参数，SA-1B数据集训练

**SEEM (Segment Everything Everywhere Model)**：
- 多模态提示分割
- 同时支持语义、实例、全景分割
- 可组合的提示输入

**X-Decoder**：
- 基于文本和其他提示的分割
- 跨任务共享表示和解码
- 统一语义和实例理解

## 4. 图像分割的关键技术

### 4.1 特征提取与表示

- **骨干网络**：ResNet、EfficientNet等
- **多尺度特征**：特征金字塔、多路径网络
- **上下文信息**：全局池化、注意力机制
- **位置编码**：用于Transformer架构

### 4.2 分割网络设计

- **空洞卷积**：扩大感受野而不增加参数
- **跳跃连接**：融合低层和高层特征
- **注意力机制**：通道注意力、空间注意力
- **边界细化**：边界感知模块、边界损失
- **动态卷积**：内容自适应的滤波器生成

### 4.3 损失函数设计

- **交叉熵损失**：像素级分类损失
- **Dice损失**：处理类别不平衡
- **Lovász-Softmax损失**：直接优化IoU
- **边界感知损失**：增强边界准确性
- **组合损失**：多目标联合优化
- **焦点损失（Focal Loss）**：处理难易样本不均衡

### 4.4 后处理技术

- **条件随机场（CRF）**：细化分割边界
- **多尺度测试**：融合多个尺度的预测
- **测试时增强（TTA）**：翻转、旋转等变换
- **实例合并**：解决重叠和冲突
- **非极大值抑制（NMS）**：消除冗余预测
- **滑窗拼接**：处理大尺寸图像

## 5. 图像分割评估指标

### 5.1 像素准确率

- **像素准确率**：正确分类的像素比例
- **类别准确率**：每个类别的像素准确率
- **平均像素准确率（mPA）**：所有类别准确率的平均值

### 5.2 交并比（IoU）

- **IoU**：预测区域与真实区域的交集除以并集
- **平均IoU（mIoU）**：所有类别IoU的平均值
- **频权IoU**：考虑类别频率的加权IoU

### 5.3 边界评估指标

- **边界F1分数**：边界检测的精确率和召回率的调和平均
- **边界IoU**：边界区域的IoU
- **Hausdorff距离**：测量边界之间的距离

### 5.4 实例分割指标

- **AP（Average Precision）**：不同IoU阈值下的平均精度
- **AP@[.5:.95]**：多个IoU阈值（0.5到0.95）的平均AP
- **AP50**：IoU阈值为0.5时的AP
- **AP75**：IoU阈值为0.75时的AP

### 5.5 全景分割指标

- **PQ（Panoptic Quality）**：结合分割质量和识别质量
- **SQ（Segmentation Quality）**：匹配实例的平均IoU
- **RQ（Recognition Quality）**：F1分数

## 6. 图像分割数据集

### 6.1 通用场景数据集

- **PASCAL VOC**：20个类别，语义分割
- **COCO**：80个类别，实例和全景分割
- **Cityscapes**：30个类别，城市街景分割
- **ADE20K**：150个类别，场景解析
- **PASCAL Context**：400+类别，详细场景标注
- **Mapillary Vistas**：66个类别，街景分割
- **SA-1B**：1100万张图像，11亿掩码，通用分割

### 6.2 专业领域数据集

- **医学影像**：
  - **ISBI**：电子显微镜图像分割
  - **BRATS**：脑肿瘤分割
  - **PROMISE12**：前列腺分割
  - **ISIC**：皮肤病变分割

- **遥感影像**：
  - **ISPRS**：航空影像分割
  - **DeepGlobe**：卫星图像分割
  - **iSAID**：遥感实例分割

- **视频分割**：
  - **DAVIS**：视频对象分割
  - **YouTube-VOS**：视频实例分割
  - **KITTI-STEP**：自动驾驶场景时空全景分割

## 7. 图像分割应用场景

### 7.1 医学影像分析

- **器官分割**：定位和测量器官
- **病变检测**：肿瘤、病灶分割
- **手术规划**：3D重建和可视化
- **细胞分析**：细胞计数和形态学分析
- **血管分割**：血管网络提取和分析

### 7.2 自动驾驶

- **道路场景理解**：道路、车道线、障碍物分割
- **可行驶区域分析**：确定车辆可通行区域
- **交通参与者识别**：行人、车辆、交通标志分割
- **环境3D重建**：结合深度信息的语义重建
- **多传感器融合**：结合雷达、相机进行场景分割

### 7.3 遥感影像分析

- **土地覆盖分类**：森林、农田、城市区域分割
- **变化检测**：不同时期的地表变化分析
- **灾害评估**：洪水、火灾等灾害区域分割
- **城市规划**：建筑物、道路网络分割
- **农业监测**：作物健康状况、产量估计

### 7.4 增强现实

- **场景理解**：环境的语义分割
- **对象交互**：实例分割支持的交互
- **虚实融合**：基于分割的遮挡处理
- **人像分割**：自拍美化、背景替换
- **动态场景分析**：移动设备上的实时分割

### 7.5 工业检测

- **缺陷检测**：产品表面缺陷分割
- **零部件识别**：工业组件的实例分割
- **质量控制**：产品质量评估
- **装配线监控**：生产过程监控
- **机器人导航**：工业环境语义理解

### 7.6 视频编辑与内容创作

- **视频对象分割**：视频中的对象追踪和分割
- **绿幕替代**：无绿幕背景替换
- **视觉特效**：基于分割的特效应用
- **内容修改**：选择性内容编辑
- **人物抠像**：专业视频制作中的人物分离

## 8. 图像分割的优化技术

### 8.1 模型轻量化

- **网络剪枝**：移除冗余连接
- **知识蒸馏**：从大模型向小模型迁移知识
- **低秩分解**：减少卷积层参数
- **量化**：降低参数精度
- **神经网络架构搜索（NAS）**：自动寻找高效架构

### 8.2 训练策略优化

- **数据增强**：旋转、缩放、颜色扰动等
- **迁移学习**：预训练模型微调
- **课程学习**：从简单到复杂的训练策略
- **半监督学习**：利用未标注数据
- **自监督预训练**：不依赖标签的表示学习

### 8.3 推理加速

- **模型剪枝**：减少计算量
- **TensorRT优化**：GPU推理优化
- **量化推理**：INT8/FP16推理
- **硬件加速**：专用NPU/TPU
- **算子融合**：减少内存访问和计算开销

## 9. 代码实现示例

### 9.1 使用PyTorch实现简单U-Net分割网络

```python
import torch
import torch.nn as nn
import torch.nn.functional as F

class DoubleConv(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.double_conv(x)

class UNet(nn.Module):
    def __init__(self, in_channels=3, out_channels=1, features=[64, 128, 256, 512]):
        super().__init__()
        self.downs = nn.ModuleList()
        self.ups = nn.ModuleList()
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2)

        # 下采样部分
        for feature in features:
            self.downs.append(DoubleConv(in_channels, feature))
            in_channels = feature

        # 上采样部分
        for feature in reversed(features):
            self.ups.append(
                nn.ConvTranspose2d(
                    feature*2, feature, kernel_size=2, stride=2,
                )
            )
            self.ups.append(DoubleConv(feature*2, feature))

        self.bottleneck = DoubleConv(features[-1], features[-1]*2)
        self.final_conv = nn.Conv2d(features[0], out_channels, kernel_size=1)

    def forward(self, x):
        skip_connections = []

        # 编码器部分
        for down in self.downs:
            x = down(x)
            skip_connections.append(x)
            x = self.pool(x)

        x = self.bottleneck(x)
        skip_connections = skip_connections[::-1]  # 反转列表

        # 解码器部分
        for idx in range(0, len(self.ups), 2):
            x = self.ups[idx](x)
            skip_connection = skip_connections[idx//2]

            # 处理输入尺寸不匹配问题
            if x.shape != skip_connection.shape:
                x = F.interpolate(x, size=skip_connection.shape[2:], mode="bilinear", align_corners=True)

            concat_skip = torch.cat((skip_connection, x), dim=1)
            x = self.ups[idx+1](concat_skip)

        return self.final_conv(x)

# 使用示例
if __name__ == "__main__":
    x = torch.randn((3, 3, 160, 160))  # (批次大小, 通道, 高度, 宽度)
    model = UNet()
    preds = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {preds.shape}")
    assert preds.shape == (3, 1, 160, 160)
```

### 9.2 使用DeepLabV3+进行语义分割

```python
# 使用PyTorch和torchvision中预训练的DeepLabV3+模型
import torch
import torchvision.transforms as T
from torchvision.models.segmentation import deeplabv3_resnet101
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np

# 载入预训练模型
model = deeplabv3_resnet101(pretrained=True)
model.eval()
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

# 预处理函数
def preprocess_image(image_path):
    input_image = Image.open(image_path).convert("RGB")
    # 预处理变换
    preprocess = T.Compose([
        T.ToTensor(),
        T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ])
    input_tensor = preprocess(input_image)
    input_batch = input_tensor.unsqueeze(0).to(device)
    
    return input_batch, input_image

# PASCAL VOC数据集的类别
CLASSES = [
    '__background__', 'aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus',
    'car', 'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike',
    'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor'
]

# 为每个类别生成随机颜色
np.random.seed(42)
COLORS = np.random.randint(0, 255, size=(len(CLASSES), 3), dtype=np.uint8)

# 进行预测
def segment_image(image_path):
    input_batch, original_image = preprocess_image(image_path)
    
    with torch.no_grad():
        output = model(input_batch)['out'][0]
    
    output_predictions = output.argmax(0).cpu().numpy()
    
    # 可视化结果
    segmented_image = Image.fromarray(COLORS[output_predictions].astype(np.uint8))
    
    # 创建图例
    plt.figure(figsize=(15, 10))
    plt.subplot(1, 2, 1)
    plt.imshow(original_image)
    plt.title("原始图像")
    plt.axis('off')
    
    plt.subplot(1, 2, 2)
    plt.imshow(segmented_image)
    plt.title("分割结果")
    plt.axis('off')
    
    # 添加图例
    handles = [plt.Rectangle((0,0),1,1, color=[c/255.0 for c in COLORS[i]]) for i in range(len(CLASSES))]
    plt.figlegend(handles, CLASSES, loc='lower center', ncol=7, bbox_to_anchor=(0.5, -0.15))
    
    plt.tight_layout()
    plt.savefig('segmentation_result.png', bbox_inches='tight')
    plt.show()
    
    return output_predictions, segmented_image

# 使用示例
# segment_image("path_to_your_image.jpg")
```

### 9.3 使用Segment Anything Model (SAM)

```python
# 使用SAM模型进行交互式分割
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from segment_anything import sam_model_registry, SamPredictor

# 加载预训练SAM模型
def load_sam_model(checkpoint_path, model_type="vit_h", device="cuda"):
    sam = sam_model_registry[model_type](checkpoint=checkpoint_path)
    sam.to(device=device)
    return SamPredictor(sam)

# 交互式分割
def interactive_segmentation(predictor, image_path, input_points=None, input_boxes=None):
    # 加载图像
    image = np.array(Image.open(image_path).convert("RGB"))
    
    # 设置图像
    predictor.set_image(image)
    
    # 生成掩码
    masks, scores, logits = predictor.predict(
        point_coords=input_points,
        box=input_boxes,
        multimask_output=True,
    )
    
    # 可视化结果
    plt.figure(figsize=(10, 10))
    plt.imshow(image)
    
    # 显示点击点（如果有）
    if input_points is not None:
        plt.scatter(input_points[:, 0], input_points[:, 1], color='red', marker='*', s=100)
    
    # 显示边界框（如果有）
    if input_boxes is not None:
        box = input_boxes
        plt.plot([box[0], box[2], box[2], box[0], box[0]], 
                 [box[1], box[1], box[3], box[3], box[1]], 'r-', linewidth=2)
    
    # 显示最佳掩码
    best_mask_idx = np.argmax(scores)
    mask = masks[best_mask_idx]
    plt.imshow(mask, alpha=0.5, cmap='jet')
    
    plt.axis('off')
    plt.title(f"分割结果 (置信度: {scores[best_mask_idx]:.3f})")
    plt.savefig('sam_segmentation_result.png')
    plt.show()
    
    return masks, scores

# 使用示例
"""
# 下载SAM模型权重
# !wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

# 加载预测器
predictor = load_sam_model("sam_vit_h_4b8939.pth")

# 定义点或框
points = np.array([[500, 375]])  # [[x, y]]
point_labels = np.array([1])  # 1表示前景点，0表示背景点

# 执行交互式分割
masks, scores = interactive_segmentation(
    predictor, 
    "path_to_image.jpg", 
    input_points=np.concatenate([points, point_labels[:, None]], axis=1),
)
"""
```

## 10. 最新研究进展

### 10.1 基础模型时代的图像分割

**基础视觉模型**：
- **SAM (Segment Anything Model)**：Meta AI发布的通用分割模型，能够接受各种提示（点击、框、掩码）并生成高质量的掩码。
- **SEEM**：支持多模态提示的分割模型，包括文本、点、框等。
- **OneFormer**：统一各类分割任务的模型，使用查询指导的掩码注意力解码器。

**语言引导的分割**：
- **CLIPSeg**：结合CLIP的零样本分割模型。
- **OpenSeeD**：开放词汇分割模型，支持语言指导的分割。
- **Grounded-SAM**：结合Grounding DINO和SAM，实现文本引导的精确分割。

### 10.2 视频分割新趋势

**多目标视频分割**：
- **XMEM**：记忆增强的视频目标分割框架。
- **DEVA**：长视频实例分割，处理对象出现、消失和重新出现。

**时空一致性建模**：
- **MaskFormer-Video**：扩展MaskFormer到视频领域，确保时间连贯性。
- **ST-DETR**：时空变换器用于视频目标跟踪。

### 10.3 多模态和3D分割

**3D医学图像分割**：
- **3D U-Net++**：3D体积数据的高效分割架构。
- **TransUNet**：结合Transformer和U-Net的3D医学影像分割。

**点云分割**：
- **PointNet++**：层次化点云特征学习。
- **MinkowskiNet**：稀疏4D卷积用于点云分割。

### 10.4 低资源和高效分割

**少样本和无监督分割**：
- **STEGO**：自监督语义分割方法，无需标注数据。
- **LSeg**：语言驱动的少样本分割。

**高效实时分割**：
- **PP-LiteSeg**：移动设备上的实时分割。
- **BiSeNetV3**：速度与准确性平衡的实时分割网络。

### 10.5 分割中的新损失函数

- **BoundaryLoss**：专注于边界精确度的损失函数。
- **RMI (Region Mutual Information)**：区域级别的相关性建模。
- **EMA (Expectation-Maximization Attention)**：期望最大化注意力损失。

## 11. 总结

图像分割作为计算机视觉的核心任务，已经从传统的基于规则和特征的方法发展到深度学习驱动的端到端方法。语义分割、实例分割和全景分割各自解决不同层次的场景理解需求，并在医疗、自动驾驶、遥感等众多领域发挥重要作用。

随着深度学习技术的不断进步，图像分割呈现出以下几个发展趋势：

1. **大型基础模型**：如SAM等基础模型展现出强大的通用分割能力，将重新定义分割任务的方法论。

2. **多模态分割**：融合文本、图像、点云等多种模态信息，实现更加智能的场景理解。

3. **高效分割**：通过模型压缩、知识蒸馏等技术，实现在边缘设备上的实时分割。

4. **无/弱监督学习**：减少对大量标注数据的依赖，利用自监督和弱监督方法提高模型性能。

5. **跨域泛化**：提高模型在不同领域和场景下的泛化能力，减少域适应需求。

图像分割技术的进步将持续推动计算机视觉在工业、医疗、自动驾驶等领域的应用，为人工智能的发展提供坚实基础。

## 参考资料

1. Long, J., Shelhamer, E., & Darrell, T. (2015). Fully convolutional networks for semantic segmentation. CVPR.

2. Ronneberger, O., Fischer, P., & Brox, T. (2015). U-net: Convolutional networks for biomedical image segmentation. MICCAI.

3. He, K., Gkioxari, G., Dollár, P., & Girshick, R. (2017). Mask r-cnn. ICCV.

4. Chen, L. C., Zhu, Y., Papandreou, G., Schroff, F., & Adam, H. (2018). Encoder-decoder with atrous separable convolution for semantic image segmentation. ECCV.

5. Kirillov, A., He, K., Girshick, R., Rother, C., & Dollár, P. (2019). Panoptic segmentation. CVPR.

6. Cheng, B., Schwing, A. G., & Kirillov, A. (2021). Per-pixel classification is not all you need for semantic segmentation. NeurIPS.

7. Xie, E., Wang, W., Yu, Z., Anandkumar, A., Alvarez, J. M., & Luo, P. (2021). SegFormer: Simple and efficient design for semantic segmentation with transformers. NeurIPS.

8. Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., ... & Girshick, R. (2023). Segment anything. arXiv preprint.

9. Zhang, S., Gu, J., Feng, G., Sui, D., Khademi, A., & Zhao, F. (2023). SEEM: Segment Everything Everywhere with Mask Transformers. arXiv preprint.

10. Zhang, Y., Zhang, S., Qing, Z., & Bai, Y. (2023). Grounding DINO: Marrying DINO with Grounded Pre-Training for Open-Set Object Detection. arXiv preprint. 