# 6.1 NLP基础

## 概述
自然语言处理(NLP)是人工智能的一个分支，专注于使计算机理解、解释和生成人类语言。NLP技术已广泛应用于日常生活中，如搜索引擎、智能助手、机器翻译和文本分析等领域。

## 文本预处理
文本预处理是NLP的基础步骤，将原始文本转化为结构化数据：

- **分词(Tokenization)**：将文本分割为最小单位（词、字符或子词）
  - 示例："我喜欢自然语言处理" → ["我", "喜欢", "自然语言", "处理"]
  - 工具：NLTK, spaCy, jieba(中文)

- **词干提取(Stemming)与词形还原(Lemmatization)**：
  - 词干提取：移除词缀得到词根，如"running" → "run"
  - 词形还原：将词转换为基本形式，如"better" → "good"
  - 区别：词形还原考虑语言学规则，结果更准确

- **停用词过滤**：移除常见但信息量少的词（如"的"、"是"、"the"、"and"）
  - 作用：减少噪声，提高处理效率
  - 示例："我是一个学生" → ["学生"]

- **文本清洗**：移除特殊字符、HTML标签、统一大小写等
  - 案例：网页抓取数据处理、社交媒体文本分析

## 文本表示
将文本转换为计算机可处理的数值表示：

- **词袋模型(BoW)**：统计文本中每个词的出现次数
  - 原理：文本表示为词频向量
  - 优点：简单直观；缺点：忽略词序和语义
  - 应用：文本分类、垃圾邮件过滤

- **TF-IDF**：词频-逆文档频率
  - 原理：TF(词频)×IDF(逆文档频率)，平衡词在文档和语料库中的重要性
  - 公式：TF-IDF(t,d) = TF(t,d) × log(N/DF(t))
  - 应用：搜索引擎结果排序、文档相似度计算

- **词嵌入**：将词映射到低维连续向量空间
  - **Word2Vec**：基于上下文预测词(CBOW)或基于词预测上下文(Skip-gram)
  - **GloVe**：基于全局词共现统计
  - **FastText**：考虑子词信息，适合处理未登录词
  - 应用案例：语义搜索、词义消歧

- **上下文化嵌入**：动态生成考虑上下文的词表示
  - **BERT**：双向Transformer编码器，考虑词的完整上下文
  - **GPT**：单向Transformer解码器，适合生成任务
  - 应用：问答系统、情感分析、命名实体识别

## 基础NLP任务

- **词性标注(POS)**：标识词的语法类别(如名词、动词)
  - 例子："The quick brown fox jumps" → [DET, ADJ, ADJ, NOUN, VERB]
  - 应用：句法分析、信息抽取

- **命名实体识别(NER)**：识别文本中的实体并分类
  - 类别：人名、地点、组织、日期等
  - 例子："乔布斯创立了苹果公司" → [人名:乔布斯, 组织:苹果公司]
  - 应用：智能客服、信息提取、知识图谱构建

- **依存句法分析**：确定句子中词语之间的语法关系
  - 例子："我喜欢这本书" → 主语("我")、谓语("喜欢")、宾语("书")
  - 应用：问答系统、机器翻译

- **情感分析**：判断文本情感倾向(正面、负面、中性)
  - 例子："这款产品非常好用" → 正面(Positive)
  - 应用：产品评论分析、社交媒体监控、市场调研

## 评估指标
衡量NLP模型性能的常用指标：

- **精确率、召回率、F1分数**：
  - 精确率 = 正确预测的正例 / 所有预测为正例的样本
  - 召回率 = 正确预测的正例 / 所有实际正例
  - F1 = 2 × (精确率 × 召回率) / (精确率 + 召回率)
  - 应用：文本分类、实体识别等任务评估

- **BLEU分数**：测量生成文本与参考文本的相似度
  - 原理：基于n-gram精确匹配计算
  - 应用：机器翻译、文本生成评估

- **困惑度(Perplexity)**：评估语言模型预测下一个词的能力
  - 越低越好，表示模型对文本的预测越准确
  - 应用：语言模型评估

## 实际应用案例

1. **搜索引擎**：结合分词、TF-IDF和语义分析提高搜索精度
   - 例：谷歌搜索引入BERT提升搜索结果相关性

2. **智能客服**：
   - 意图识别：分析用户问题类型
   - 实体提取：识别关键信息
   - 示例：银行app中的智能助手可以回答"我的信用卡额度是多少"

3. **情感分析系统**：
   - 电商平台产品评论分析
   - 社交媒体品牌口碑监控
   - 案例：分析Twitter上对新产品发布的公众反应

4. **内容推荐**：
   - 基于文本表示的相似度计算
   - 案例：新闻app根据用户阅读历史推荐相关文章

## NLP资源与工具

- **常用工具库**：
  - NLTK：Python自然语言工具包
  - spaCy：工业级NLP库
  - Transformers：Hugging Face开发的预训练模型库

- **语料库和数据集**：
  - WordNet：英语词汇数据库
  - SQuAD：斯坦福问答数据集
  - GLUE：自然语言理解基准测试

- **开发建议**：
  - 从预训练模型开始，使用迁移学习节省时间和资源
  - 注意数据质量和标注准确性
  - 根据实际应用场景选择合适的评估指标 