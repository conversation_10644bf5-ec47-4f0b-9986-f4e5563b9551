# 数据工程

## 概述
数据工程是AI系统开发中的关键环节，负责数据的采集、存储、处理和管理，为机器学习和人工智能应用提供高质量的数据基础。优质的数据工程是成功AI项目的先决条件，能够显著影响模型性能和项目成果。

## 数据采集与获取
- 数据来源多样化：公开数据集、爬虫采集、传感器数据、用户生成内容等
- 采样策略：随机采样、分层采样、时序采样、主动学习采样
- 数据质量控制：初步过滤、异常检测、冗余排除
- 法律与伦理考量：隐私保护、知识产权、合规采集
- 数据采集自动化：定时任务、事件触发采集、增量更新

## 数据预处理与清洗
- 缺失值处理：插补、删除、特殊标记
- 异常值识别与处理：统计方法、基于模型方法
- 格式标准化：统一数据格式、单位转换、编码规范
- 重复数据处理：精确匹配、模糊匹配、实体解析
- 数据降噪：滤波、平滑处理、信号处理技术
- 时序数据预处理：重采样、对齐、季节性调整

## 数据转换与特征工程
- 数值转换：缩放、标准化、正则化、对数变换
- 离散化与编码：独热编码、标签编码、频率编码
- 特征提取：主成分分析、自编码器、领域特定特征
- 特征选择：过滤方法、包装方法、嵌入式方法
- 降维技术：PCA、t-SNE、UMAP、LDA
- 时间特征：日期分解、时间窗口统计、序列特征
- 文本处理：分词、词袋模型、TF-IDF、词嵌入
- 图像处理：颜色直方图、边缘检测、纹理特征

## 数据标注与增强
- 标注方法：人工标注、众包标注、半自动标注
- 标注质量控制：标注者一致性评估、标准操作流程
- 主动学习：不确定性采样、多样性采样、期望误差减少
- 数据增强技术：
  - 图像增强：旋转、翻转、缩放、色彩变换、剪切
  - 文本增强：回译、同义词替换、句法树操作
  - 音频增强：加噪、时间伸缩、音高变换
  - 表格数据增强：SMOTE、ADASYN、混合策略
- 合成数据生成：GAN、VAE、物理模拟

## 数据存储与管理系统
- 数据格式：CSV、JSON、Parquet、Avro、HDF5、TFRecord
- 文件系统：本地存储、分布式文件系统、对象存储
- 数据库选型：关系型、文档型、列式存储、图数据库、时序数据库
- 大数据存储：HDFS、S3、云存储解决方案
- 数据仓库与数据湖：Snowflake、Redshift、Delta Lake
- 特征存储：离线/在线双存储、特征服务、版本控制
- 元数据管理：数据目录、血缘追踪、版本控制

## 数据处理框架与技术
- 批处理系统：Hadoop MapReduce、Spark、Dask
- 流处理系统：Kafka Streams、Flink、Spark Streaming
- ETL/ELT工具：Airflow、Prefect、dbt
- 分布式计算：参数服务器、数据并行、AllReduce
- 处理优化：内存管理、计算调度、数据局部性
- 数据处理编排：DAG工作流、调度策略、依赖管理

## 数据质量与监控
- 数据质量指标：完整性、准确性、一致性、及时性
- 数据验证：模式验证、约束检查、统计检验
- 异常检测：统计方法、基于模型的方法、监督式检测
- 数据漂移监控：特征分布变化、概念漂移、协变量偏移
- 数据健康检查：自动检测、警报机制、修复流程
- 质量报告与可视化：仪表盘、时间序列分析、趋势监控

## 数据治理与安全
- 数据生命周期管理：创建、存储、使用、归档、删除
- 数据访问控制：身份验证、授权策略、细粒度权限
- 敏感数据处理：匿名化、假名化、差分隐私
- 数据合规性：GDPR、CCPA、行业特定法规
- 数据溯源：血缘分析、版本控制、审计日志
- 数据策略制定：标准化流程、最佳实践、培训

## 可扩展性与性能优化
- 横向扩展架构：分区策略、负载均衡、一致性保证
- 纵向优化：内存优化、IO优化、计算优化
- 分布式存储优化：数据本地性、复制策略、缓存机制
- 数据压缩：列式压缩、字典编码、增量编码
- 查询优化：索引设计、查询计划、物化视图
- 资源调度：弹性计算资源、优先级管理、成本控制

## MLOps中的数据工程
- 数据版本控制：DVC、Git LFS、Delta Lake
- 特征存储与服务：Feature Store、在线特征计算
- 实验跟踪：数据集版本、特征集记录、再现性保障
- CI/CD中的数据管理：数据测试、自动化数据流
- 模型-数据反馈循环：数据漂移检测、自动重训练触发
- 数据和模型的协同版本管理：联合版本控制、依赖跟踪

## 新兴技术与趋势
- 数据合成与增强的生成AI应用：使用大型生成模型创建训练数据
- 自动化特征工程：AutoML在特征选择与生成中的应用
- 去中心化数据管理：区块链、联邦学习数据协议
- 实时数据处理：低延迟流处理、即时特征计算
- 数据网格架构：分布式数据所有权、数据产品思维
- 基础模型时代的数据工程：适配大型语言模型的数据准备流程 