# AI知识体系导航

## 导航说明
本文档为AI知识体系的主导航页，提供了完整的AI学习路径和知识结构。通过编号体系可以清晰地了解各知识点之间的关系和层次结构。

## 文件组织规则
1. **文件命名规则**：所有文件和目录以数字编号开头（如 1.1、2.3），按照层级和顺序排列
2. **目录结构**：主要知识领域为一级目录（如 1-基础理论、2-机器学习），具体主题为二级文件
3. **复杂主题分解**：特别复杂的主题（如 7.8-RAG、12.7-Agent）可创建子目录进一步细分
4. **新增内容规则**：
   - 新主题应放入最相关的现有目录，遵循现有编号系统
   - 如需创建新的一级目录，使用下一个可用的序号（如当前最高为13，则新目录为14）
   - 子主题应使用小数点标记层级（如 14.1、14.2）
5. **文件更新**：更新内容时保持原有文件结构和编号不变，除非需要进行主要重组
6. **交叉引用**：相关主题之间应通过Markdown链接进行交叉引用，以构建知识网络

## 知识体系概览

### 1-基础理论
- 1.1 [数学基础](1-基础理论/1.1-数学基础.md) - 线性代数、概率统计、微积分、信息论等
- 1.2 [统计学基础](1-基础理论/1.2-统计学基础.md) - 假设检验、回归分析、实验设计等
- 1.3 [信息论基础](1-基础理论/1.3-信息论基础.md) - 熵、互信息、KL散度等
- 1.4 [计算复杂度理论](1-基础理论/1.4-计算复杂度理论.md) - 算法分析、计算类别等
- 1.5 [优化理论](1-基础理论/1.5-优化理论.md) - 凸优化、梯度下降、约束优化等
- 1.6 [控制论基础](1-基础理论/1.6-控制论基础.md) - 反馈控制、系统稳定性等

### 2-机器学习
- 2.1 [监督学习](2-机器学习/2.1-监督学习.md) - 分类、回归算法
- 2.2 [非监督学习](2-机器学习/2.2-非监督学习.md) - 聚类、降维、异常检测
- 2.3 [半监督学习](2-机器学习/2.3-半监督学习.md) - 标签传播、自训练等
- 2.4 [迁移学习](2-机器学习/2.4-迁移学习.md) - 领域适应、知识迁移等
- 2.5 [元学习](2-机器学习/2.5-元学习.md) - 学会学习、少样本学习等
- 2.6 [联邦学习](2-机器学习/2.6-联邦学习.md) - 分布式隐私保护学习
- 2.7 [自监督学习](2-机器学习/2.7-自监督学习.md) - 对比学习、掩码预测等

### 3-深度学习
- 3.1 [神经网络基础](3-深度学习/3.1-神经网络基础.md) - 前向传播、反向传播、激活函数
- 3.2 [卷积神经网络](3-深度学习/3.2-卷积神经网络.md) - 卷积层、池化层、经典架构
- 3.3 [循环神经网络](3-深度学习/3.3-循环神经网络.md) - RNN、LSTM、GRU
- 3.4 [Transformer架构](3-深度学习/3.4-Transformer架构.md) - 自注意力机制、多头注意力
- 3.5 [图神经网络](3-深度学习/3.5-图神经网络.md) - 图卷积、消息传递等
- 3.6 [生成对抗网络](3-深度学习/3.6-生成对抗网络.md) - GAN架构、训练技巧等
- 3.7 [变分自编码器](3-深度学习/3.7-变分自编码器.md) - VAE原理与应用
- 3.8 [扩散模型](3-深度学习/3.8-扩散模型.md) - 扩散过程、采样技术等

### 4-强化学习
- 4.1 [RL基础](4-强化学习/4.1-RL基础.md) - MDP、价值函数、策略
- 4.2 [策略梯度方法](4-强化学习/4.2-策略梯度方法.md) - REINFORCE、PPO等
- 4.3 [值函数方法](4-强化学习/4.3-值函数方法.md) - Q-learning、DQN等
- 4.4 [模型型强化学习](4-强化学习/4.4-模型型强化学习.md) - 规划、模拟等
- 4.5 [多Agent强化学习](4-强化学习/4.5-多Agent强化学习.md) - 协作、竞争策略等
- 4.6 [模仿学习](4-强化学习/4.6-模仿学习.md) - 行为克隆、逆强化学习等
- 4.7 [离线强化学习](4-强化学习/4.7-离线强化学习.md) - 批量RL、保守Q学习等

### 5-计算机视觉
- 5.1 [CV基础](5-计算机视觉/5.1-CV基础.md) - 图像处理、特征提取
- 5.2 [图像分类](5-计算机视觉/5.2-图像分类.md) - 分类模型、迁移学习等
- 5.3 [目标检测](5-计算机视觉/5.3-目标检测.md) - 一阶段、二阶段检测器等
- 5.4 [图像分割](5-计算机视觉/5.4-图像分割.md) - 语义分割、实例分割等
- 5.5 [图像生成](5-计算机视觉/5.5-图像生成.md) - 风格迁移、图像合成等
- 5.6 [视频理解](5-计算机视觉/5.6-视频理解.md) - 动作识别、视频分析等
- 5.7 [三维视觉](5-计算机视觉/5.7-三维视觉.md) - 深度估计、点云处理等

### 6-自然语言处理
- 6.1 [NLP基础](6-自然语言处理/6.1-NLP基础.md) - 文本表示、预处理技术
- 6.2 [文本分类与情感分析](6-自然语言处理/6.2-文本分类与情感分析.md) - 分类模型、情感词典等
- 6.3 [机器翻译](6-自然语言处理/6.3-机器翻译.md) - 统计翻译、神经翻译等
- 6.4 [问答系统](6-自然语言处理/6.4-问答系统.md) - 检索式QA、生成式QA等
- 6.5 [文本生成](6-自然语言处理/6.5-文本生成.md) - 语言模型、文本摘要等
- 6.6 [信息抽取](6-自然语言处理/6.6-信息抽取.md) - 命名实体识别、关系抽取等
- 6.7 [对话系统](6-自然语言处理/6.7-对话系统.md) - 任务型对话、开放域对话等
- 6.8 [语音识别(ASR)](6-自然语言处理/6.8-语音识别(ASR).md) - 声学模型、语言模型、端到端ASR等
- 6.9 [语音合成(TTS)](6-自然语言处理/6.9-语音合成(TTS).md) - 声学模型、声码器、表现力合成等

### 7-大模型技术
- 7.1 [大型语言模型](7-大模型技术/7.1-大型语言模型.md) - GPT系列、LLaMA等
- 7.2 [扩散模型](7-大模型技术/7.2-扩散模型.md) - Stable Diffusion、DALL-E等
- 7.3 [多模态大模型](7-大模型技术/7.3-多模态大模型.md) - GPT-4V、Gemini等
- 7.4 [预训练范式](7-大模型技术/7.4-预训练范式.md) - 自回归、掩码语言模型等
- 7.5 [微调技术](7-大模型技术/7.5-微调技术.md) - LoRA、P-tuning等
- 7.6 [RLHF与人类对齐](7-大模型技术/7.6-RLHF与人类对齐.md) - 偏好学习、宪法AI等
- 7.7 [大模型评估](7-大模型技术/7.7-大模型评估.md) - 基准测试、能力评估等
- 7.8 [RAG](7-大模型技术/7.8-RAG/) - RAG技术与应用
  - 7.8.1 [RAG基础架构](7-大模型技术/7.8-RAG/7.8.1-RAG基础架构.md) - RAG核心组件、流程、向量搜索
  - 7.8.2 [高级RAG技术](7-大模型技术/7.8-RAG/7.8.2-高级RAG技术.md) - 多步检索、自适应检索、混合检索
  - 7.8.3 [RAG评估框架](7-大模型技术/7.8-RAG/7.8.3-RAG评估框架.md) - 检索质量评估、答案相关性评估
  - 7.8.4 [Agentic RAG](7-大模型技术/7.8-RAG/7.8.4-Agentic RAG.md) - RAG与Agent结合、ReAct架构

### 8-多模态学习
- 8.1 [多模态基础](8-多模态学习/8.1-多模态基础.md) - 跨模态表示、对齐
- 8.2 [视觉-语言模型](8-多模态学习/8.2-视觉-语言模型.md) - CLIP、BLIP等
- 8.3 [音频-视觉模型](8-多模态学习/8.3-音频-视觉模型.md) - 音视频融合技术
- 8.4 [跨模态检索](8-多模态学习/8.4-跨模态检索.md) - 文本-图像检索等
- 8.5 [多模态融合技术](8-多模态学习/8.5-多模态融合技术.md) - 早期融合、晚期融合等
- 8.6 [多模态生成](8-多模态学习/8.6-多模态生成.md) - 文本到图像、视频生成等
- 8.7 [多模态理解](8-多模态学习/8.7-多模态理解.md) - 视觉问答、场景理解等

### 9-AI系统与工程
- 9.1 [模型部署](9-AI系统与工程/9.1-模型部署.md) - 服务化、优化技术
- 9.2 [分布式训练](9-AI系统与工程/9.2-分布式训练.md) - 数据并行、模型并行等
- 9.3 [自动机器学习](9-AI系统与工程/9.3-自动机器学习.md) - 超参优化、架构搜索等
- 9.4 [数据工程](9-AI系统与工程/9.4-数据工程.md) - 数据管理、标注、增强等
- 9.5 [MLOps](9-AI系统与工程/9.5-MLOps.md) - 模型生命周期管理、CI/CD等
- 9.6 [高性能计算](9-AI系统与工程/9.6-高性能计算.md) - GPU优化、分布式计算等
- 9.7 [AI硬件加速](9-AI系统与工程/9.7-AI硬件加速.md) - TPU、FPGA、ASIC等

### 10-应用场景
- 10.1 [AI应用概述](10-应用场景/10.1-AI应用概述.md) - 主要应用领域综述
- 10.2 [医疗AI](10-应用场景/10.2-医疗AI.md) - 医学影像、药物发现等
- 10.3 [金融AI](10-应用场景/10.3-金融AI.md) - 风险评估、算法交易等
- 10.4 [工业AI](10-应用场景/10.4-工业AI.md) - 预测性维护、质量控制等
- 10.5 [教育AI](10-应用场景/10.5-教育AI.md) - 个性化学习、智能辅导等
- 10.6 [创意AI](10-应用场景/10.6-创意AI.md) - 内容生成、创意辅助等
- 10.7 [科学AI](10-应用场景/10.7-科学AI.md) - 科学发现、模拟与预测等

### 11-伦理与安全
- 11.1 [AI伦理](11-伦理与安全/11.1-AI伦理.md) - 道德准则、伦理框架
- 11.2 [AI安全](11-伦理与安全/11.2-AI安全.md) - 对抗攻击、鲁棒性等
- 11.3 [隐私保护](11-伦理与安全/11.3-隐私保护.md) - 差分隐私、联邦学习等
- 11.4 [公平与偏见](11-伦理与安全/11.4-公平与偏见.md) - 算法偏见、公平性度量等
- 11.5 [可解释性](11-伦理与安全/11.5-可解释性.md) - 可解释AI技术、透明度等
- 11.6 [对齐问题](11-伦理与安全/11.6-对齐问题.md) - 价值对齐、安全对齐等
- 11.7 [法律法规框架](11-伦理与安全/11.7-法律法规框架.md) - 国际准则、监管政策等

### 12-前沿研究
- 12.1 [人工通用智能(AGI)](12-前沿研究/12.1-人工通用智能(AGI).md) - 通用智能研究、评估等
- 12.2 [神经符号融合](12-前沿研究/12.2-神经符号融合.md) - 符号推理与神经网络结合
- 12.3 [大模型涌现能力](12-前沿研究/12.3-大模型涌现能力.md) - 能力涌现机制研究
- 12.4 [因果推理](12-前沿研究/12.4-因果推理.md) - 因果发现、干预等
- 12.5 [认知架构](12-前沿研究/12.5-认知架构.md) - 认知模型、心智模拟等
- 12.6 [持续学习](12-前沿研究/12.6-持续学习.md) - 终身学习、灾难性遗忘等
- 12.7 [Agent](12-前沿研究/12.7-Agent/) - Agent架构、自主决策等
  - 12.7.1 [Agent基础架构](12-前沿研究/12.7-Agent/12.7.1-Agent基础架构.md) - 感知-决策-行动循环、BDI架构
  - 12.7.2 [Agent工具使用能力](12-前沿研究/12.7-Agent/12.7.2-Agent工具使用能力.md) - 工具类型、工具操作基础
  - 12.7.3 [多Agent系统](12-前沿研究/12.7-Agent/12.7.3-多Agent系统.md) - Agent协作、Agent竞争、多Agent通信
  - 12.7.4 [自主学习Agent](12-前沿研究/12.7-Agent/12.7.4-自主学习Agent.md) - 探索与利用策略、记忆与知识更新
  - 12.7.5 [Agent通信协议](12-前沿研究/12.7-Agent/12.7.5-Agent通信协议.md) - A2A协议、MCP协议、状态同步机制

### 13-语音处理
- 13.1 [语音处理基础](13-语音处理/13.1-语音处理基础.md) - 语音信号特性、特征提取、数字信号处理
- 13.2 [说话人识别与验证](13-语音处理/13.2-说话人识别与验证.md) - 声纹识别、说话人分割与聚类
- 13.3 [语音增强与分离](13-语音处理/13.3-语音增强与分离.md) - 噪声抑制、回声消除、语音分离

## 学习路径指南

### 初学者路径（0-6个月）
1. 1.1 数学基础 → 2.1 监督学习 → 3.1 神经网络基础
2. 6.1 NLP基础 或 5.1 CV基础（根据兴趣选择）
3. 11.1 AI伦理（了解AI发展的社会影响）

### 进阶学习者路径（6-18个月）
1. 3.2 卷积神经网络 → 3.3 循环神经网络 → 3.4 Transformer架构
2. 4.1 强化学习基础
3. 8.1 多模态基础
4. 9.1 模型部署（实践技能）

### 专业研究者路径（18+个月）
1. 7.1-7.7 大模型技术（全部）
2. 8.2-8.7 高级多模态技术（全部）
3. 12.1-12.7 前沿研究方向（根据专业方向选择）
4. 14.1-14.9 AI面试准备（求职前必读）

## 延伸资源
- [AI领域整体资料与面试资料](AI领域整体资料与面试资料.md) - AI学习资源与面试准备 

## 知识体系完成状态

### 已完成文件
- 基础理论：1.1-数学基础、1.2-统计学基础、1.3-信息论基础、1.4-计算复杂度理论、1.5-优化理论、1.6-控制论基础
- 机器学习：2.1-监督学习、2.2-非监督学习、2.3-半监督学习、2.4-迁移学习、2.5-元学习、2.6-联邦学习、2.7-自监督学习
- 深度学习：3.1-神经网络基础、3.2-卷积神经网络、3.3-循环神经网络、3.4-Transformer架构、3.5-图神经网络、3.6-生成对抗网络、3.7-变分自编码器、3.8-扩散模型
- 强化学习：4.1-RL基础、4.2-策略梯度方法、4.3-值函数方法、4.4-模型型强化学习、4.5-多Agent强化学习、4.6-模仿学习、4.7-离线强化学习
- 计算机视觉：5.1-CV基础、5.2-图像分类、5.3-目标检测、5.4-图像分割、5.5-图像生成、5.6-视频理解、5.7-三维视觉
- 自然语言处理：6.1-NLP基础、6.2-文本分类与情感分析、6.3-机器翻译、6.4-问答系统、6.5-文本生成、6.6-信息抽取、6.7-对话系统、6.8-语音识别(ASR)、6.9-语音合成(TTS)
- 大模型技术：7.1-大型语言模型、7.2-扩散模型、7.3-多模态大模型、7.4-预训练范式、7.5-微调技术、7.6-RLHF与人类对齐、7.7-大模型评估、7.8系列(RAG技术)
- 多模态学习：8.1-多模态基础、8.2-视觉-语言模型、8.3-音频-视觉模型、8.4-跨模态检索、8.5-多模态融合技术、8.6-多模态生成、8.7-多模态理解
- AI系统与工程：9.1-模型部署、9.2-分布式训练、9.3-自动机器学习、9.4-数据工程、9.5-MLOps、9.6-高性能计算、9.7-AI硬件加速
- 应用场景：10.1-AI应用概述、10.2-医疗AI、10.3-金融AI、10.4-工业AI、10.5-教育AI、10.6-创意AI、10.7-科学AI
- 伦理与安全：11.1-AI伦理、11.2-AI安全、11.3-隐私保护、11.4-公平与偏见、11.5-可解释性、11.6-对齐问题、11.7-法律法规框架
- 前沿研究：12.1-人工通用智能(AGI)、12.2-神经符号融合、12.3-大模型涌现能力、12.4-因果推理、12.5-认知架构、12.6-持续学习、12.7系列(自主智能体)
- 语音处理：13.1-语音处理基础、13.2-说话人识别与验证、13.3-语音增强与分离

### 待创建文件
- 14-AI面试准备：14.1-面试基础知识、14.2-算法与编程面试、14.3-机器学习面试、14.4-深度学习面试、14.5-NLP面试、14.6-CV面试、14.7-系统设计面试、14.8-大厂面经汇总、14.9-简历与作品集准备 

## 面试资料

### 14-AI面试准备
- 14.1 [面试基础知识](14-AI面试准备/14.1-面试基础知识.md) - 常见面试问题与解答、面试流程、自我介绍技巧
- 14.2 [算法与编程面试](14-AI面试准备/14.2-算法与编程面试.md) - 常见算法题、数据结构应用、编程实践题
- 14.3 [机器学习面试](14-AI面试准备/14.3-机器学习面试.md) - 经典ML算法原理、超参数调优、模型评估与选择
- 14.4 [深度学习面试](14-AI面试准备/14.4-深度学习面试.md) - 神经网络架构、优化方法、模型调试技巧
- 14.5 [NLP面试](14-AI面试准备/14.5-NLP面试.md) - 文本处理技术、语言模型、序列建模问题
- 14.6 [CV面试](14-AI面试准备/14.6-CV面试.md) - 图像处理算法、目标检测原理、模型评估方法
- 14.7 [系统设计面试](14-AI面试准备/14.7-系统设计面试.md) - AI系统架构设计、扩展性考量、分布式训练
- 14.8 [大厂面经汇总](14-AI面试准备/14.8-大厂面经汇总.md) - 顶级科技公司面试流程与案例分析
- 14.9 [简历与作品集准备](14-AI面试准备/14.9-简历与作品集准备.md) - 简历优化技巧、项目展示、作品集构建 