# 7.1 数字绘画软件入门

> 了解数字绘画的基础软件和核心概念  
> 创建：2024年6月

## 📚 学习目标

- 了解主流数字绘画软件的特点与适用场景
- 掌握数字绘画的基本术语与概念
- 理解数字绘画工作流程的核心要素
- 为进一步学习数字绘画技法打下基础

## 🔍 数字绘画概述

### 什么是数字绘画

数字绘画（Digital Painting）是使用电脑软件和数位板等设备，在数字环境中进行的绘画创作。与传统绘画相比，数字绘画具有以下特点：

- **可逆性**：随时可以撤销、重做操作
- **灵活性**：图层系统、变换工具等提供更大创作自由
- **多样性**：可以模拟各种传统媒介效果
- **便捷性**：不需要准备实体材料，方便存储与分享
- **效率性**：能更快地进行修改和调整

### 数字绘画的应用领域

- 概念艺术（Concept Art）
- 游戏与电影插画
- 漫画与动画创作
- 商业插画
- 数字艺术（Digital Art）
- UI/UX设计

## 🖥️ 主流数字绘画软件

### Adobe Photoshop
**特点**：行业标准软件，功能全面，适合多种创作需求
**优势**：
- 强大的图层与蒙版系统
- 丰富的画笔自定义选项
- 与其他Adobe软件无缝集成
- 大量可用的教程和插件

**适用场景**：专业插画、概念设计、照片绘画混合创作

### Clip Studio Paint (原MANGA STUDIO)
**特点**：专为插画、漫画和动画创作设计
**优势**：
- 优秀的线稿工具
- 漫画专用功能（分格、对话框等）
- 逼真的传统媒介模拟
- 相对友好的价格（一次性购买）

**适用场景**：漫画创作、动漫风插画、线稿精细的作品

### Procreate (仅iPad)
**特点**：移动端最强大的绘画应用之一
**优势**：
- 直观的触控界面
- 流畅的绘图体验
- 强大但不复杂的功能集
- 丰富的画笔库

**适用场景**：移动创作、速写、中小型插画项目

### Krita
**特点**：功能强大的开源绘画软件
**优势**：
- 完全免费且开源
- 专业级画笔引擎
- 丰富的画笔预设
- 直观的界面和自定义选项

**适用场景**：预算有限的艺术家、数字绘画入门、概念插画

### Corel Painter
**特点**：最逼真的传统媒介模拟
**优势**：
- 超过900种画笔
- 最真实的传统媒介效果
- 独特的颜料混合系统
- 专业的画布纹理

**适用场景**：追求传统绘画质感、传统艺术家转型数字

### 对比与选择指南

| 软件 | 价格 | 学习曲线 | 适用风格 | 平台 |
|------|------|----------|----------|------|
| Photoshop | 订阅制 | 中高 | 全能型 | Win/Mac |
| Clip Studio | 一次性/订阅 | 中等 | 漫画/动漫 | Win/Mac/iOS |
| Procreate | 一次性 | 低 | 插画/概念 | 仅iOS |
| Krita | 免费 | 中等 | 全能型 | Win/Mac/Linux |
| Painter | 一次性/订阅 | 高 | 传统风格 | Win/Mac |

**初学者建议**：
- 预算有限：Krita是非常好的免费选择
- 动漫风格：Clip Studio Paint较为专业
- iPad用户：优先考虑Procreate
- 长期职业发展：熟悉Photoshop是必要的

## 📝 数字绘画基本概念

### 分辨率与画布大小

**分辨率**：图像的精细程度，通常以PPI（每英寸像素数）表示
- 屏幕显示：72-96 PPI
- 打印需求：300 PPI或更高
- 概念稿：150-200 PPI通常足够

**画布尺寸**：
- 尺寸越大，细节表现空间越多，但占用资源也越多
- 建议以目标用途的1.5-2倍尺寸创建（便于裁剪和调整）
- 常见错误：以过小尺寸创建，导致放大时失真

### 图层系统

数字绘画的核心优势之一，允许将不同元素分离在不同层面：

**基本图层类型**：
- 普通图层：最常用，存放图像内容
- 调整图层：非破坏性地改变底层图像
- 图层蒙版：控制图层的部分可见性
- 剪贴蒙版：将一个图层限制在下方图层的形状内

**图层管理建议**：
- 使用有意义的图层命名
- 将相关图层组织在图层组中
- 定期合并不再需要单独调整的图层
- 保留关键阶段的图层备份

### 色彩模式

**RGB**：
- 屏幕显示用，加色模式
- 每个像素由红、绿、蓝三原色组成
- 适合数字作品、网络发布

**CMYK**：
- 印刷用，减色模式
- 青、品红、黄、黑四色组成
- 色域比RGB小，某些鲜艳颜色无法准确显示

**色彩深度**：
- 8位/通道：每通道256级灰度，适合大多数插画
- 16位/通道：每通道65536级灰度，适合需要大量色彩调整的作品

### 数位板与压感

**压感级别**：
- 现代设备通常有8192级压感
- 实际上2048级以上差异不大
- 压感允许根据按压力度改变线条粗细、透明度等

**倾斜识别**：
- 高级数位板可识别笔的倾斜角度
- 模拟传统媒介中笔刷的角度变化效果
- 对模拟铅笔侧面涂抹、毛笔倾斜等效果很有用

**数位板推荐**：
- 入门级：Wacom Intuos系列、XP-Pen Deco系列
- 中级：Wacom Intuos Pro、XP-Pen Artist系列
- 高级：Wacom Cintiq、Huion Kamvas Pro系列
- 高端便携：iPad Pro + Apple Pencil + Procreate

## 🎨 数字绘画工作流程

### 1. 前期准备
- 确定画布尺寸和分辨率
- 建立基本图层结构
- 收集参考素材
- 设定色彩环境（如果需要）

### 2. 草图阶段
- 使用单独图层创建草图
- 利用低透明度和变换工具探索构图
- 使用基本形状建立结构
- 确定画面主要元素

### 3. 线稿阶段
- 基于草图创建清晰线条
- 可使用矢量工具获得精确线条
- 调整线条粗细表现层次
- 完成结构细节

### 4. 上色阶段
- 建立基础色块
- 确定主要光源和阴影
- 逐步增加色彩层次和细节
- 调整色彩协调性

### 5. 细节与效果
- 强化主要焦点区域
- 添加材质和细节
- 增加特殊效果（光晕、粒子等）
- 统一画面风格

### 6. 最终调整
- 整体色彩平衡
- 对比度和饱和度调整
- 锐化和模糊效果
- 出口前的格式优化

## 💡 入门建议

### 初学者常见问题
1. **设备焦虑**：先掌握基础，而非追求高端设备
2. **软件困惑**：选一款软件深入学习，而非尝试多款
3. **跳过基础**：数字绘画依然需要扎实的传统绘画知识
4. **过度依赖**：不要过分依赖撤销、变换等数字工具
5. **忽视备份**：养成定期保存和版本备份的习惯

### 学习建议
- 从模仿传统媒介开始，再探索数字特有技法
- 专注于一种风格，掌握后再拓展
- 加入数字艺术社区获取反馈
- 参考专业艺术家的工作流程视频
- 建立个人资源库（笔刷、纹理、色板等）

## 📚 拓展资源

### 学习网站
- 国内：站酷、CGWANG、翼狐网
- 国外：Ctrl+Paint、Proko、Schoolism

### YouTube频道
- Sinix Design
- Marc Brunet
- Feng Zhu Design
- Aaron Blaise

### 在线社区
- ArtStation
- Behance
- DeviantArt
- Pixiv

---

*数字绘画结合了传统艺术技巧与现代技术可能性，为艺术家提供了空前的创作自由。掌握数字绘画的基础概念和工具，将为你打开一个充满可能性的创作世界。* 