# 个人知识管理系统

## 目录结构

```
.
├── 1能力
│   ├── 1学习能力
│   └── 3交际能力
├── 2知识
│   ├── 1科学
│   ├── 2哲学
│   ├── 3国学
│   ├── 4宗教
│   ├── 5心理学
│   ├── 6经济学
│   ├── 7管理学
│   └── 8决策
├── 3专业
│   ├── 1.4.2项目管理
│   ├── 1.4.3IT技术
│   └── 1.4.4语言能力
├── 4兴趣
│   ├── 1健康生活
│   └── 2职业规划
└── 5实践
```

## 使用说明

1. 能力：包含个人发展的基础能力
   - 学习能力：学习方法、知识获取等
   - 交际能力：沟通技巧、人际关系等

2. 知识：按照学科体系组织的知识
   - 科学：数学、物理、化学、生物、地理等基础科学
   - 哲学：哲学思想、逻辑学等
   - 国学：中国传统文化、经典著作等
   - 宗教：各种宗教思想和历史
   - 心理学：基础心理学、应用心理学等
   - 经济学：宏观经济、微观经济等
   - 管理学：管理理论、管理方法等
   - 决策：决策理论、决策方法等

3. 专业：专业领域知识
   - 项目管理：项目管理知识体系、方法论等
   - IT技术：编程、系统设计、网络等
   - 语言能力：英语学习、词汇、语法等

4. 兴趣：个人兴趣相关
   - 健康生活：身心健康、生活习惯
   - 职业规划：职业发展、技能提升

5. 实践：实践应用
   - 各类实践项目和应用

## 更新日志

- 2024-06-11 v3.0
  - 移除目录名称中的空格和前导零
  - 简化目录结构，提高导航效率

- 2024-03-21 v2.4
  - 将心理学内容从核心思想移动到社会科学分类下
  - 更新目录结构说明

# PDF压缩工具

这是一个简单的PDF压缩工具，可以将PDF文件压缩到指定大小（默认30MB）以下。

## 功能特点

- 自动压缩PDF文件中的图像
- 逐步降低图像质量直到达到目标大小
- 保持文档结构和文本质量
- 显示压缩进度和文件大小信息

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

```bash
python compress_pdf.py <input_pdf>
```

例如：
```bash
python compress_pdf.py compressed_screenshots.pdf
```

压缩后的文件将保存为原文件名加上"_compressed"后缀。

## 注意事项

- 压缩过程主要通过降低图像质量来实现
- 如果文件已经小于目标大小（30MB），脚本将不会进行压缩
- 压缩质量从90开始，每次降低10，最低到20
- 如果达到最低质量仍无法达到目标大小，将报告压缩失败
