#!/usr/bin/env python3
import os
import re
import sys
from datetime import datetime
from PIL import Image
import io
from PyPDF2 import PdfWriter, PdfReader
import math

# 获取当前目录或指定目录中的所有PNG文件
def get_png_files(directory='.'):
    png_files = [f for f in os.listdir(directory) if f.lower().endswith('.png')]
    return png_files

# 定义一个函数来从文件名中提取日期和时间
def extract_datetime(filename):
    # 文件名格式：Screenshot 2024-08-07 at 09.32.37.png
    match = re.search(r'Screenshot (\d{4}-\d{2}-\d{2}) at (\d{2})\.(\d{2})\.(\d{2})\.png', filename)
    if match:
        date_str = match.group(1)
        hour = match.group(2)
        minute = match.group(3)
        second = match.group(4)
        dt = datetime.strptime(f"{date_str} {hour}:{minute}:{second}", "%Y-%m-%d %H:%M:%S")
        return dt
    return datetime.min  # 如果无法解析，返回最小日期

# 获取文件大小（MB）
def get_file_size_mb(file_path):
    return os.path.getsize(file_path) / (1024 * 1024)

def calculate_image_size(img_width, img_height, page_width, page_height, margin=40):
    """计算图片在页面上的最佳尺寸，保持纵横比，并留有边距"""
    available_width = page_width - 2 * margin
    available_height = page_height - 2 * margin
    
    width_ratio = available_width / img_width
    height_ratio = available_height / img_height
    scale = min(width_ratio, height_ratio)
    
    new_width = int(img_width * scale)
    new_height = int(img_height * scale)
    
    return new_width, new_height

def get_folder_name(directory):
    """获取文件夹名称"""
    return os.path.basename(os.path.normpath(directory))

# 处理图片并转换为PDF页面
def process_images(png_files, directory='.', quality=85, target_size_mb=30, output_pdf="compressed_screenshots.pdf"):
    print(f"\n正在处理 {len(png_files)} 个图片...")
    
    # A4页面尺寸（像素，300DPI）
    A4_WIDTH = 2480  # 8.27 inches * 300 DPI
    A4_HEIGHT = 3508  # 11.69 inches * 300 DPI
    
    # 创建PDF写入器
    pdf_writer = PdfWriter()
    
    # 处理每个图片
    for i, png_file in enumerate(png_files, 1):
        file_path = os.path.join(directory, png_file)
        
        try:
            # 打开图片
            with Image.open(file_path) as img:
                # 计算最佳尺寸
                new_width, new_height = calculate_image_size(
                    img.width, img.height, A4_WIDTH, A4_HEIGHT
                )
                
                # 调整大小
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 如果是彩色图像，考虑转换为灰度
                if img.mode in ('RGBA', 'RGB'):
                    # 检查图像是否主要是黑白的
                    is_mostly_bw = is_black_white_image(img)
                    if is_mostly_bw:
                        img = img.convert('L')  # 转换为灰度
                
                # 创建新的白色背景A4页面
                a4_page = Image.new('RGB', (A4_WIDTH, A4_HEIGHT), 'white')
                
                # 计算居中位置
                x = (A4_WIDTH - new_width) // 2
                y = (A4_HEIGHT - new_height) // 2
                
                # 将调整后的图片粘贴到A4页面上
                a4_page.paste(img, (x, y))
                
                # 保存为临时文件
                temp_pdf = f"temp_{i}.pdf"
                a4_page.save(temp_pdf, format='PDF', resolution=300, optimize=True, quality=quality)
                
                # 读取临时PDF并添加到主PDF
                reader = PdfReader(temp_pdf)
                for page in reader.pages:
                    pdf_writer.add_page(page)
                
                # 删除临时文件
                os.remove(temp_pdf)
        except Exception as e:
            print(f"处理图片 {png_file} 时出错: {str(e)}")
        
        # 显示进度
        if i % 10 == 0 or i == len(png_files):
            print(f"已处理: {i}/{len(png_files)} 图片")
    
    # 保存PDF
    with open(output_pdf, "wb") as f:
        pdf_writer.write(f)
    
    # 返回文件大小
    return get_file_size_mb(output_pdf)

# 检查图像是否主要是黑白的
def is_black_white_image(img, threshold=0.9):
    # 转换为灰度进行分析
    if img.mode != 'L':
        gray_img = img.convert('L')
    else:
        gray_img = img
    
    # 采样分析像素
    pixels = list(gray_img.getdata())
    total_pixels = len(pixels)
    
    # 计算有多少像素接近黑色或白色
    bw_pixels = sum(1 for p in pixels if p < 30 or p > 225)
    
    # 如果大部分像素是黑色或白色，则认为是黑白图像
    return bw_pixels / total_pixels > threshold

def main():
    # 检查是否提供了目录参数
    directory = '.'
    if len(sys.argv) > 1:
        directory = sys.argv[1]
        if not os.path.isdir(directory):
            print(f"错误：{directory} 不是一个有效的目录")
            return
    
    # 获取文件夹名称
    folder_name = get_folder_name(directory)
    
    # 获取PNG文件
    png_files = get_png_files(directory)
    
    if not png_files:
        print(f"在目录 {directory} 中未找到PNG文件")
        return
    
    # 按照日期和时间对文件进行排序
    png_files.sort(key=extract_datetime)
    
    # 打印排序后的文件列表
    print(f"找到 {len(png_files)} 个PNG文件，按时间顺序排序...")
    for i, f in enumerate(png_files[:5], 1):
        print(f"{i}. {f}")
    if len(png_files) > 5:
        print(f"... 以及其他 {len(png_files) - 5} 个文件")
    
    # 将文件列表分成两半
    mid_point = math.ceil(len(png_files) / 2)
    first_half = png_files[:mid_point]
    second_half = png_files[mid_point:]
    
    # 初始质量参数
    quality = 85
    min_quality = 20
    
    # 处理第一部分
    print("\n处理第一部分PDF...")
    while quality >= min_quality:
        print(f"\n尝试压缩参数: 质量={quality}")
        output_pdf = f"{folder_name}_part1.pdf"
        pdf_size = process_images(first_half, directory, quality, output_pdf=output_pdf)
        print(f"生成的PDF大小: {pdf_size:.2f}MB")
        
        if pdf_size <= 30:
            print(f"\n成功! 已创建大小为 {pdf_size:.2f}MB 的PDF文件 (第一部分)")
            break
        
        quality -= 10
        if quality < min_quality:
            print("\n警告: 无法将PDF压缩到30MB以下。")
            break
    
    # 重置质量参数处理第二部分
    quality = 85
    print("\n处理第二部分PDF...")
    while quality >= min_quality:
        print(f"\n尝试压缩参数: 质量={quality}")
        output_pdf = f"{folder_name}_part2.pdf"
        pdf_size = process_images(second_half, directory, quality, output_pdf=output_pdf)
        print(f"生成的PDF大小: {pdf_size:.2f}MB")
        
        if pdf_size <= 30:
            print(f"\n成功! 已创建大小为 {pdf_size:.2f}MB 的PDF文件 (第二部分)")
            break
        
        quality -= 10
        if quality < min_quality:
            print("\n警告: 无法将PDF压缩到30MB以下。")
            break
    
    # 打印最终结果
    part1_size = get_file_size_mb(f"{folder_name}_part1.pdf")
    part2_size = get_file_size_mb(f"{folder_name}_part2.pdf")
    print(f"\n已成功创建两个PDF文件:")
    print(f"第一部分 ({folder_name}_part1.pdf): {part1_size:.2f}MB, 包含 {len(first_half)} 个图片")
    print(f"第二部分 ({folder_name}_part2.pdf): {part2_size:.2f}MB, 包含 {len(second_half)} 个图片")

if __name__ == "__main__":
    main() 