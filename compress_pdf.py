import os
import sys
from PyPDF2 import Pdf<PERSON>eader, PdfWriter
from PIL import Image
import io

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    return os.path.getsize(file_path) / (1024 * 1024)

def compress_pdf(input_path, output_path, target_size_mb=30):
    """压缩PDF文件到指定大小"""
    print(f"开始压缩PDF: {input_path}")
    print(f"目标大小: {target_size_mb}MB")
    
    # 初始质量参数
    quality = 90
    min_quality = 20
    
    while quality >= min_quality:
        try:
            reader = PdfReader(input_path)
            writer = PdfWriter()
            
            # 处理每一页
            for page in reader.pages:
                # 复制页面
                writer.add_page(page)
                
                # 获取页面上的图像对象
                for image_file_object in page.images:
                    # 读取图像数据
                    image = Image.open(io.BytesIO(image_file_object.data))
                    
                    # 压缩图像
                    img_buffer = io.BytesIO()
                    image.save(img_buffer, format=image.format if image.format else 'JPEG', 
                             quality=quality, optimize=True)
                    
                    # 更新PDF中的图像
                    image_file_object.data = img_buffer.getvalue()
            
            # 保存压缩后的PDF
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)
            
            # 检查输出文件大小
            output_size = get_file_size_mb(output_path)
            print(f"当前文件大小: {output_size:.2f}MB (质量: {quality})")
            
            if output_size <= target_size_mb:
                print(f"压缩成功！最终文件大小: {output_size:.2f}MB")
                return True
            
            # 如果文件仍然太大，降低质量继续尝试
            quality -= 10
            
        except Exception as e:
            print(f"压缩过程中出错: {str(e)}")
            return False
    
    print("无法将文件压缩到目标大小")
    return False

def main():
    if len(sys.argv) != 2:
        print("使用方法: python compress_pdf.py <input_pdf>")
        return
    
    input_pdf = sys.argv[1]
    if not os.path.exists(input_pdf):
        print(f"错误：找不到输入文件 {input_pdf}")
        return
    
    # 生成输出文件名
    output_pdf = input_pdf.rsplit('.', 1)[0] + '_compressed.pdf'
    
    # 获取原始文件大小
    original_size = get_file_size_mb(input_pdf)
    print(f"原始文件大小: {original_size:.2f}MB")
    
    # 如果文件已经小于目标大小，直接复制
    if original_size <= 30:
        print("文件已经小于30MB，无需压缩")
        return
    
    # 压缩文件
    if compress_pdf(input_pdf, output_pdf):
        print(f"压缩后的文件已保存为: {output_pdf}")
    else:
        print("压缩失败")

if __name__ == "__main__":
    main() 